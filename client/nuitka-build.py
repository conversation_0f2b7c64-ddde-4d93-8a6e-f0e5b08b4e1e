#!/usr/bin/env python3
"""
Nuitka编译脚本
使用Nuitka将Python代码编译成原生可执行文件，提供最高性能和源代码保护
一键安装所有依赖并构建
"""

import os
import sys
import shutil
import subprocess
import multiprocessing
from pathlib import Path
import platform

def check_environment():
    """检查运行环境"""
    print("🔍 检查运行环境...")

    project_root = Path(__file__).parent
    src_dir = project_root / "src"

    # 检查源码目录
    if not src_dir.exists():
        print(f"❌ 源码目录不存在: {src_dir}")
        print("请确保在正确的项目目录中运行此脚本")
        return False

    # 检查main.py
    main_file = src_dir / "main.py"
    if not main_file.exists():
        print(f"❌ 主程序文件不存在: {main_file}")
        print("请确保src/main.py文件存在")
        return False

    print(f"✅ 项目根目录: {project_root}")
    print(f"✅ 源码目录: {src_dir}")
    print(f"✅ 主程序文件: {main_file}")

    return True

def install_patchelf_manually():
    """手动安装patchelf"""
    print("📦 手动安装patchelf...")

    # 检查patchelf是否已安装
    try:
        subprocess.run(["patchelf", "--version"], check=True, capture_output=True)
        print("✅ patchelf已安装")
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        pass

    try:
        # 下载patchelf源码
        print("📥 下载patchelf源码...")
        subprocess.run([
            "wget", "-O", "patchelf-0.18.0.tar.gz",
            "https://github.com/NixOS/patchelf/releases/download/0.18.0/patchelf-0.18.0.tar.gz"
        ], check=True, capture_output=True)

        # 解压
        print("📦 解压patchelf...")
        subprocess.run(["tar", "-xzf", "patchelf-0.18.0.tar.gz"], check=True, capture_output=True)

        # 编译安装
        print("🔨 编译安装patchelf...")
        os.chdir("patchelf-0.18.0")
        subprocess.run(["./configure", "--prefix=/usr/local"], check=True, capture_output=True)

        # 获取CPU核心数用于并行编译
        cpu_count = multiprocessing.cpu_count()
        jobs = max(1, cpu_count - 1)
        print(f"🚀 使用 {jobs} 个并行任务编译patchelf")
        subprocess.run(["make", f"-j{jobs}"], check=True, capture_output=True)
        subprocess.run(["sudo", "make", "install"], check=True, capture_output=True)

        # 清理
        os.chdir("..")
        subprocess.run(["rm", "-rf", "patchelf-0.18.0", "patchelf-0.18.0.tar.gz"],
                      check=True, capture_output=True)

        print("✅ patchelf手动安装成功")
        return True

    except subprocess.CalledProcessError as e:
        print(f"❌ patchelf手动安装失败: {e}")
        return False
    except Exception as e:
        print(f"❌ patchelf安装过程中发生错误: {e}")
        return False

def check_and_install_system_deps():
    """检查并安装系统依赖"""
    print("🔍 检查系统依赖...")

    # 检查操作系统
    if platform.system() != "Linux":
        print("⚠️  警告: 此脚本主要为Linux系统设计")

    # 检查是否有sudo权限
    try:
        subprocess.run(["sudo", "-n", "true"], check=True, capture_output=True)
        has_sudo = True
    except (subprocess.CalledProcessError, FileNotFoundError):
        has_sudo = False
        print("⚠️  警告: 没有sudo权限，可能无法安装系统依赖")

    # 安装系统依赖
    if has_sudo:
        try:
            print("📦 更新包管理器...")
            subprocess.run(["sudo", "apt", "update"], check=True, capture_output=True)

            print("📦 安装系统依赖...")
            system_packages = [
                "python3-pip",
                "python3-dev",
                "python3-venv",
                "build-essential",
                "gcc",
                "g++",
                "make",
                "libc6-dev",
                "libssl-dev",
                "libffi-dev",
                "zlib1g-dev",
                "ccache",     # 加速编译
                "wget",       # 下载工具
                "autoconf",   # 编译工具
                "automake",   # 编译工具
                "libtool"     # 编译工具
            ]

            subprocess.run(["sudo", "apt", "install", "-y"] + system_packages,
                         check=True, capture_output=True)
            print("✅ 系统依赖安装完成")

        except subprocess.CalledProcessError as e:
            print(f"⚠️  系统依赖安装失败: {e}")
            print("继续尝试Python依赖安装...")

    # 尝试安装patchelf
    try:
        subprocess.run(["sudo", "apt", "install", "-y", "patchelf"],
                      check=True, capture_output=True)
        print("✅ patchelf通过apt安装成功")
    except subprocess.CalledProcessError:
        print("⚠️  apt安装patchelf失败，尝试手动安装...")
        if not install_patchelf_manually():
            print("❌ patchelf安装失败，这可能会导致Nuitka编译失败")
            return False

    return True

def install_pip():
    """确保pip可用"""
    try:
        # 测试pip是否可用
        subprocess.run([sys.executable, "-m", "pip", "--version"],
                      check=True, capture_output=True)
        print("✅ pip已可用")
        return True
    except subprocess.CalledProcessError:
        print("📦 pip不可用，尝试安装...")

        # 尝试使用get-pip.py安装
        try:
            import urllib.request
            print("📥 下载get-pip.py...")
            urllib.request.urlretrieve("https://bootstrap.pypa.io/get-pip.py", "get-pip.py")

            print("📦 安装pip...")
            subprocess.run([sys.executable, "get-pip.py"], check=True)

            # 清理下载的文件
            if os.path.exists("get-pip.py"):
                os.remove("get-pip.py")

            print("✅ pip安装成功")
            return True

        except Exception as e:
            print(f"❌ pip安装失败: {e}")
            return False

def install_python_deps():
    """安装Python依赖"""
    print("📦 安装Python依赖...")

    # 升级pip
    try:
        subprocess.run([sys.executable, "-m", "pip", "install", "--upgrade", "pip"],
                      check=True, capture_output=True)
        print("✅ pip已升级")
    except subprocess.CalledProcessError as e:
        print(f"⚠️  pip升级失败: {e}")

    # 安装基础依赖
    base_packages = [
        "wheel",
        "setuptools",
        "build"
    ]

    for package in base_packages:
        try:
            subprocess.run([sys.executable, "-m", "pip", "install", package],
                          check=True, capture_output=True)
            print(f"✅ {package} 安装成功")
        except subprocess.CalledProcessError as e:
            print(f"⚠️  {package} 安装失败: {e}")

    return True

def install_project_deps(project_root):
    """安装项目依赖"""
    print("📦 安装项目依赖...")

    # 检查requirements.txt
    requirements_file = project_root / "requirements.txt"
    if requirements_file.exists():
        try:
            print(f"📋 从 {requirements_file} 安装依赖...")
            subprocess.run([sys.executable, "-m", "pip", "install", "-r", str(requirements_file)],
                          check=True)
            print("✅ requirements.txt 依赖安装成功")
        except subprocess.CalledProcessError as e:
            print(f"⚠️  requirements.txt 依赖安装失败: {e}")

    # 安装常用的Web框架依赖
    common_packages = [
        "fastapi",
        "uvicorn[standard]",
        "pydantic",
        "python-dotenv",
        "aiofiles",
        "python-multipart"
    ]

    print("📦 安装常用Web框架依赖...")
    for package in common_packages:
        try:
            subprocess.run([sys.executable, "-m", "pip", "install", package],
                          check=True, capture_output=True)
            print(f"✅ {package} 安装成功")
        except subprocess.CalledProcessError as e:
            print(f"⚠️  {package} 安装失败: {e}")

    return True

def install_nuitka():
    """安装Nuitka"""
    try:
        import nuitka  # noqa: F401
        print("✅ Nuitka已安装")
        return True
    except ImportError:
        print("📦 安装Nuitka...")
        try:
            subprocess.run([sys.executable, "-m", "pip", "install", "nuitka"], check=True)
            print("✅ Nuitka安装成功")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ Nuitka安装失败: {e}")
            return False

def build_with_nuitka():
    """使用Nuitka编译"""

    print("=== RK3588客户端Nuitka一键编译工具 ===")
    print("🚀 开始一键安装和构建流程...")

    # 0. 检查运行环境
    if not check_environment():
        print("❌ 运行环境检查失败")
        return False

    # 1. 检查并安装系统依赖
    if not check_and_install_system_deps():
        print("❌ 系统依赖检查失败")
        return False

    # 2. 确保pip可用
    if not install_pip():
        print("❌ pip安装失败")
        return False

    # 3. 安装Python依赖
    if not install_python_deps():
        print("❌ Python依赖安装失败")
        return False

    # 4. 检查并安装Nuitka
    if not install_nuitka():
        print("❌ Nuitka安装失败")
        return False

    # 5. 安装项目依赖
    project_root = Path(__file__).parent
    if not install_project_deps(project_root):
        print("❌ 项目依赖安装失败")
        return False

    src_dir = project_root / "src"
    build_dir = project_root / "build"

    # 清理之前的构建
    if build_dir.exists():
        shutil.rmtree(build_dir)
        print("✅ 清理构建目录")

    build_dir.mkdir()

    # 获取CPU核心数用于并行编译
    cpu_count = multiprocessing.cpu_count()
    jobs = max(1, cpu_count - 1)
    print(f"🚀 使用 {jobs} 个并行任务进行Nuitka编译")

    # Nuitka编译参数
    nuitka_args = [
        sys.executable, "-m", "nuitka",
        "--standalone",  # 独立可执行文件
        "--onefile",  # 单文件模式
        "--output-dir=" + str(build_dir),  # 输出目录
        "--output-filename=rk3588_client",  # 输出文件名

        # 并行编译选项
        f"--jobs={jobs}",  # 使用多线程编译
        "--lto=yes",  # 启用链接时优化

        # 优化选项
        "--enable-plugin=anti-bloat",  # 减少文件大小
        "--assume-yes-for-downloads",  # 自动下载依赖
        "--static-libpython=no",  # 禁用静态libpython（解决Anaconda问题）
        "--show-progress",  # 显示编译进度

        # 包含数据文件
        "--include-data-dir=static=static",  # 静态文件

        # 包含模块
        "--include-module=uvicorn",
        "--include-module=fastapi",
        "--include-module=pydantic",
        "--include-module=asyncio",
        "--include-module=socket",
        "--include-module=struct",
        "--include-module=json",
        "--include-module=logging",

        # 主程序
        str(src_dir / "main.py")
    ]

    try:
        print("🔨 开始Nuitka编译...")
        print(f"📋 编译命令: {' '.join(nuitka_args)}")
        print("⏳ 编译可能需要几分钟时间，请耐心等待...")

        # 实时显示编译输出
        process = subprocess.Popen(nuitka_args, cwd=project_root,
                                 stdout=subprocess.PIPE, stderr=subprocess.STDOUT,
                                 universal_newlines=True, bufsize=1)

        # 实时输出编译日志
        for line in process.stdout:
            print(f"📝 {line.rstrip()}")

        process.wait()

        if process.returncode == 0:
            print("✅ Nuitka编译成功！")

            # 创建部署包
            create_deployment_package(build_dir)

        else:
            print(f"❌ Nuitka编译失败，返回码: {process.returncode}")
            return False

    except subprocess.CalledProcessError as e:
        print(f"❌ Nuitka编译失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 编译过程中发生错误: {e}")
        return False

    return True

def create_deployment_package(build_dir):
    """创建部署包"""

    project_root = Path(__file__).parent
    deploy_dir = project_root / "deployment"

    # 创建部署目录
    if deploy_dir.exists():
        shutil.rmtree(deploy_dir)
    deploy_dir.mkdir()

    print("📦 创建部署包...")

    # 查找编译后的可执行文件
    executable_files = list(build_dir.glob("**/rk3588_client"))
    if not executable_files:
        executable_files = list(build_dir.glob("**/rk3588_client.bin"))

    if executable_files:
        executable = executable_files[0]
        shutil.copy2(executable, deploy_dir / "rk3588_client")
        os.chmod(deploy_dir / "rk3588_client", 0o755)
        print("✅ 复制可执行文件")
    else:
        print("❌ 未找到编译后的可执行文件")
        return

    # 复制配置文件
    config_files = [".env", "parameter_mappings.json"]
    for config_file in config_files:
        src_file = project_root / config_file
        if src_file.exists():
            shutil.copy2(src_file, deploy_dir / config_file)
            print(f"✅ 复制配置文件: {config_file}")
        else:
            print(f"⚠️  配置文件不存在: {config_file}")

    # 复制静态文件
    static_src = project_root / "static"
    if static_src.exists():
        static_dst = deploy_dir / "static"
        if static_dst.exists():
            shutil.rmtree(static_dst)
        shutil.copytree(static_src, static_dst)
        print(f"✅ 复制静态文件目录: static")

    # 创建日志目录
    logs_dir = deploy_dir / "logs"
    if not logs_dir.exists():
        logs_dir.mkdir()
        print(f"✅ 创建日志目录: logs")

    # 创建启动脚本
    create_scripts(deploy_dir)

    print(f"✅ 部署包已创建: {deploy_dir}")

def create_scripts(deploy_dir):
    """创建启动脚本"""

    # 启动脚本
    start_script = deploy_dir / "start.sh"
    with open(start_script, 'w') as f:
        f.write("""#!/bin/bash
# RK3588客户端启动脚本

echo "启动RK3588客户端..."

# 检查配置文件
if [ ! -f ".env" ]; then
    echo "错误: 未找到配置文件 .env"
    echo "请先配置 .env 文件"
    exit 1
fi

# 创建日志目录
mkdir -p logs

# 设置环境变量
export PYTHONUNBUFFERED=1

# 启动客户端
./rk3588_client

echo "客户端已停止"
""")
    os.chmod(start_script, 0o755)
    print("✅ 创建启动脚本")



if __name__ == "__main__":
    print("🚀 RK3588客户端一键编译工具启动")
    print("=" * 50)

    success = build_with_nuitka()

    print("=" * 50)
    if success:
        print("🎉 一键编译完成！")
        print("📁 部署包位置: ./deployment/")
        print("🚀 运行方式:")
        print("   cd deployment")
        print("   ./start.sh")
        print("📖 更多信息请查看 deployment/ 目录")
    else:
        print("❌ 一键编译失败")
        print("💡 请检查上面的错误信息并重试")
        sys.exit(1)
