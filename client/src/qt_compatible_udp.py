#!/usr/bin/env python3
"""
Qt兼容的UDP客户端
直接移植Qt程序的UDP通信逻辑到Python
"""

import socket
import struct
import asyncio
import logging
import time
from typing import List, Dict, Optional, Tuple


class QtCompatibleUDP:
    """Qt兼容的UDP客户端（直接移植mainwindow.cpp的逻辑）"""

    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.socket = None
        self.target_ip = ""
        self.target_port = 0
        self.listen_port = 0

        # 协议常量（从mainwindow.cpp复制）
        self.PARAM_REQUEST_SIZE = 3082
        self.FRAME_HEADER = 0xFF80
        self.FRAME_TYPE = 0x002A

        # 请求ID（从mainwindow.cpp复制）
        self.REQUEST_READ = 100
        self.REQUEST_WRITE = 101
        self.REQUEST_READ_ALL = 102

        # 响应缓存
        self.response_buffer = bytearray()
        self.expected_response_size = 2572  # 从测试中观察到的实际响应大小

    async def initialize(self, target_ip: str, target_port: int, listen_port: int) -> bool:
        """初始化UDP客户端"""
        try:
            self.target_ip = target_ip
            self.target_port = target_port
            self.listen_port = listen_port

            # 创建UDP socket
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            self.socket.bind(('', listen_port))
            self.socket.settimeout(0.1)  # 非阻塞模式

            self.logger.info(f"UDP客户端初始化成功: {target_ip}:{target_port}, 监听端口: {listen_port}")
            return True

        except Exception as e:
            self.logger.error(f"UDP客户端初始化失败: {e}")
            return False

    def create_read_all_request(self) -> bytes:
        """创建读取所有参数的请求（完全按照Qt程序的sendReadAllParamsRequest）"""
        request = bytearray(self.PARAM_REQUEST_SIZE)

        # 设置帧头
        request[0] = 0xFF
        request[1] = 0x80

        # 设置帧类型
        request[2] = 0x00
        request[3] = 0x2A

        # 设置帧长度（大端序）
        frame_length = self.PARAM_REQUEST_SIZE
        request[4] = (frame_length >> 8) & 0xFF  # 高字节
        request[5] = frame_length & 0xFF         # 低字节

        # 累加值
        request[6] = 0x00

        # 请求ID（大端序）- 读取所有参数使用102
        request[7] = (self.REQUEST_READ_ALL >> 8) & 0xFF  # 高字节
        request[8] = self.REQUEST_READ_ALL & 0xFF         # 低字节

        # 填充循环数据（1-255循环填充，从索引9开始到倒数第二个字节）
        for i in range(9, self.PARAM_REQUEST_SIZE - 1):
            request[i] = ((i - 8) % 255) + 1

        # 计算CRC（累加所有字节除了最后一个）
        crc = 0
        for i in range(self.PARAM_REQUEST_SIZE - 1):
            crc += request[i]
        request[self.PARAM_REQUEST_SIZE - 1] = crc & 0xFF

        return bytes(request)

    def create_write_request(self, param_name: str, value: float) -> bytes:
        """创建写入参数的请求（完全按照Qt程序的sendWriteRequest）"""
        request = bytearray(self.PARAM_REQUEST_SIZE)

        # 设置帧头
        request[0] = 0xFF
        request[1] = 0x80

        # 设置帧类型
        request[2] = 0x00
        request[3] = 0x2A

        # 设置帧长度（大端序）
        frame_length = self.PARAM_REQUEST_SIZE
        request[4] = (frame_length >> 8) & 0xFF  # 高字节
        request[5] = frame_length & 0xFF         # 低字节

        # 累加值
        request[6] = 0x00

        # 请求ID（大端序）- 写入参数使用101
        request[7] = (self.REQUEST_WRITE >> 8) & 0xFF  # 高字节
        request[8] = self.REQUEST_WRITE & 0xFF         # 低字节

        # 参数名称填充（从索引9开始，8字节）
        name_bytes = param_name.encode('utf-8')[:8]
        name_bytes = name_bytes.ljust(8, b'\x00')
        request[9:17] = name_bytes

        # 参数值填充（从索引9 + 256*8 = 2057开始，4字节float，小端序）
        value_bytes = struct.pack('<f', value)
        value_offset = 9 + 256 * 8
        request[value_offset:value_offset + 4] = value_bytes

        # 计算CRC（累加所有字节除了最后一个）
        crc = 0
        for i in range(self.PARAM_REQUEST_SIZE - 1):
            crc += request[i]
        request[self.PARAM_REQUEST_SIZE - 1] = crc & 0xFF

        return bytes(request)

    def create_read_request(self, param_name: str) -> bytes:
        """创建读取单个参数的请求（使用请求ID 100）"""
        request = bytearray(self.PARAM_REQUEST_SIZE)

        # 设置帧头
        request[0] = 0xFF
        request[1] = 0x80

        # 设置帧类型
        request[2] = 0x00
        request[3] = 0x2A

        # 设置帧长度（大端序）
        frame_length = self.PARAM_REQUEST_SIZE
        request[4] = (frame_length >> 8) & 0xFF  # 高字节
        request[5] = frame_length & 0xFF         # 低字节

        # 累加值
        request[6] = 0x00

        # 请求ID（大端序）- 读取单个参数使用100
        request[7] = (self.REQUEST_READ >> 8) & 0xFF  # 高字节
        request[8] = self.REQUEST_READ & 0xFF         # 低字节

        # 参数名称填充（从索引9开始，8字节）
        name_bytes = param_name.encode('utf-8')[:8]
        name_bytes = name_bytes.ljust(8, b'\x00')
        request[9:17] = name_bytes

        # 其余部分填充0（与写入请求不同，读取请求不需要填充参数值）

        # 计算CRC（累加所有字节除了最后一个）
        crc = 0
        for i in range(self.PARAM_REQUEST_SIZE - 1):
            crc += request[i]
        request[self.PARAM_REQUEST_SIZE - 1] = crc & 0xFF

        return bytes(request)

    async def send_request_with_fragmentation(self, request_data: bytes) -> bool:
        """分包发送请求（完全按照Qt程序的方式）"""
        try:
            max_packet_size = 1024  # Qt程序中的maxPacketSize

            # 分包发送
            for i in range(0, len(request_data), max_packet_size):
                packet = request_data[i:i + max_packet_size]
                self.socket.sendto(packet, (self.target_ip, self.target_port))

                # 添加10ms延迟（Qt程序中的QThread::msleep(10)）
                await asyncio.sleep(0.01)

            self.logger.debug(f"分包发送完成: {len(request_data)} 字节，{(len(request_data) + max_packet_size - 1) // max_packet_size} 包")
            return True

        except Exception as e:
            self.logger.error(f"发送请求失败: {e}")
            return False

    async def receive_response(self, timeout: float = 5.0) -> Optional[bytes]:
        """接收响应（累积接收，处理分片）"""
        self.response_buffer.clear()
        start_time = time.time()

        while time.time() - start_time < timeout:
            try:
                data, addr = self.socket.recvfrom(4096)

                # 检查是否是新的帧头
                if len(data) >= 2 and ((data[0] == 0xFF and data[1] == 0x80) or
                                      (data[0] == 0x80 and data[1] == 0xFF)):
                    # 新帧开始，重置缓存
                    self.response_buffer.clear()
                    self.logger.debug(f"检测到新帧开始，来自: {addr}")

                # 累积响应数据
                self.response_buffer.extend(data)
                self.logger.debug(f"累积响应数据: {len(self.response_buffer)} 字节，来自: {addr}")

                # 检查是否接收到完整的响应
                if len(self.response_buffer) >= self.expected_response_size:
                    self.logger.info(f"接收到完整响应: {len(self.response_buffer)} 字节")
                    return bytes(self.response_buffer[:self.expected_response_size])

            except socket.timeout:
                # 超时是正常的，继续等待
                await asyncio.sleep(0.01)
                continue
            except Exception as e:
                self.logger.error(f"接收响应失败: {e}")
                break

        self.logger.warning("接收响应超时")
        return None

    def parse_response(self, response_data: bytes) -> List[Dict[str, any]]:
        """解析响应数据（按照Qt程序的processResponse逻辑）"""
        result = []

        try:
            if len(response_data) < self.expected_response_size:
                self.logger.error(f"响应数据长度不足: {len(response_data)}")
                return result

            # 验证帧头（考虑字节序问题）
            if not ((response_data[0] == 0xFF and response_data[1] == 0x80) or
                   (response_data[0] == 0x80 and response_data[1] == 0xFF)):
                self.logger.error(f"无效的帧头: {response_data[0]:02X} {response_data[1]:02X}")
                return result

            # 解析参数（从索引9开始）
            param_start_offset = 9
            value_start_offset = param_start_offset + 256 * 8  # 2057

            for i in range(256):
                # 提取参数名（8字节）
                name_start = param_start_offset + i * 8
                name_bytes = response_data[name_start:name_start + 8]
                name = name_bytes.rstrip(b'\x00').decode('utf-8', errors='ignore')

                if not name:
                    continue

                # 提取参数值（4字节float，小端序）
                value_start = value_start_offset + i * 4
                if value_start + 4 <= len(response_data):
                    value_bytes = response_data[value_start:value_start + 4]
                    value = struct.unpack('<f', value_bytes)[0]

                    result.append({
                        'name': name,
                        'value': value,
                        'type': 'float'
                    })

            self.logger.info(f"解析到 {len(result)} 个参数")
            return result

        except Exception as e:
            self.logger.error(f"解析响应失败: {e}")
            return result

    async def read_all_parameters(self) -> List[Dict[str, any]]:
        """读取所有参数"""
        try:
            # 创建读取所有参数的请求
            request_data = self.create_read_all_request()

            # 发送请求
            if not await self.send_request_with_fragmentation(request_data):
                return []

            # 接收响应
            response_data = await self.receive_response()
            if not response_data:
                return []

            # 解析响应
            return self.parse_response(response_data)

        except Exception as e:
            self.logger.error(f"读取所有参数失败: {e}")
            return []

    async def write_parameter(self, name: str, value: float) -> bool:
        """写入单个参数"""
        try:
            # 创建写入参数的请求
            request_data = self.create_write_request(name, value)

            # 发送请求
            if not await self.send_request_with_fragmentation(request_data):
                return False

            # 对于写入请求，我们不等待响应（因为下位机可能不响应）
            # 但我们给一个短暂的等待时间，以防下位机确实会响应
            response_data = await self.receive_response(timeout=2.0)

            if response_data:
                self.logger.info(f"写入参数收到响应: {name} = {value}")
                return True
            else:
                self.logger.info(f"写入参数无响应（可能正常）: {name} = {value}")
                # 即使无响应也返回True，因为请求已发送
                return True

        except Exception as e:
            self.logger.error(f"写入参数失败: {e}")
            return False

    async def read_parameter(self, name: str) -> Optional[float]:
        """读取单个参数"""
        try:
            # 创建读取单个参数的请求
            request_data = self.create_read_request(name)

            # 发送请求
            if not await self.send_request_with_fragmentation(request_data):
                return None

            # 接收响应
            response_data = await self.receive_response()
            if not response_data:
                self.logger.warning(f"读取单个参数无响应: {name}")
                return None

            # 解析响应，查找指定参数
            params = self.parse_response(response_data)
            for param in params:
                if param['name'] == name:
                    self.logger.info(f"成功读取单个参数: {name} = {param['value']}")
                    return param['value']

            self.logger.warning(f"响应中未找到参数: {name}")
            return None

        except Exception as e:
            self.logger.error(f"读取单个参数失败: {e}")
            return None

    def close(self):
        """关闭UDP客户端"""
        if self.socket:
            try:
                self.socket.close()
                self.socket = None
                self.logger.info("UDP客户端已关闭")
            except Exception as e:
                self.logger.error(f"关闭UDP客户端失败: {e}")

    def __del__(self):
        """析构函数"""
        self.close()


# 测试函数
async def test_qt_compatible_udp():
    """测试Qt兼容的UDP客户端"""
    client = QtCompatibleUDP()

    try:
        # 初始化
        if not await client.initialize('192.168.4.160', 8080, 18004):
            print("初始化失败")
            return

        print("初始化成功")

        # 读取所有参数
        print("读取所有参数...")
        params = await client.read_all_parameters()

        if params:
            print(f"成功读取到 {len(params)} 个参数:")
            for i, param in enumerate(params[:10]):
                print(f"  {i+1:2d}. {param['name']:<20} = {param['value']:>10.6f}")

            if len(params) > 10:
                print(f"  ... 还有 {len(params) - 10} 个参数")

            # 测试写入参数
            if params:
                test_param = params[0]
                original_value = test_param['value']
                new_value = original_value + 0.1

                print(f"\n测试写入参数: {test_param['name']} = {new_value}")
                success = await client.write_parameter(test_param['name'], new_value)
                print(f"写入结果: {'成功' if success else '失败'}")

                # 等待一下再读取验证
                print("等待2秒后验证...")
                await asyncio.sleep(2)

                # 重新读取验证
                new_params = await client.read_all_parameters()
                if new_params:
                    for param in new_params:
                        if param['name'] == test_param['name']:
                            current_value = param['value']
                            print(f"验证结果: {test_param['name']} = {current_value}")

                            if abs(current_value - new_value) < 0.001:
                                print("🎉 写入成功！参数值已更新")
                            elif abs(current_value - original_value) < 0.001:
                                print("⚠️  参数值未改变，写入可能失败")
                            else:
                                print(f"❓ 参数值发生了意外变化")
                            break
        else:
            print("未读取到任何参数")

    finally:
        client.close()


if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    asyncio.run(test_qt_compatible_udp())
