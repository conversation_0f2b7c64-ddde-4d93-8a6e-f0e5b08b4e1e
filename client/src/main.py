#!/usr/bin/env python3
"""
RK3588客户端主程序
启动Web服务器和服务器连接
"""

import asyncio
import sys
import os
import json
import logging
import signal
from datetime import datetime
from typing import Optional

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 加载.env文件
def load_env_file():
    """加载.env文件到环境变量"""
    # 查找.env文件的可能位置
    possible_paths = [
        ".env",  # 当前目录
        "../.env",  # 上级目录
        os.path.join(os.path.dirname(os.path.abspath(__file__)), ".env"),  # src目录
        os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), ".env"),  # 项目根目录
    ]

    for env_path in possible_paths:
        if os.path.exists(env_path):
            print(f"找到.env文件: {env_path}")
            try:
                with open(env_path, 'r', encoding='utf-8') as f:
                    for line in f:
                        line = line.strip()
                        if line and not line.startswith('#') and '=' in line:
                            key, value = line.split('=', 1)
                            os.environ[key.strip()] = value.strip()
                print("✅ .env文件加载成功")
                return True
            except Exception as e:
                print(f"❌ 加载.env文件失败: {e}")
                continue

    print("⚠️  未找到.env文件，使用默认配置")
    return False

# 在导入其他模块之前加载环境变量
load_env_file()

from web_server import start_web_server, set_global_server_client
from server_client import ServerClient
from log_manager import setup_logging, get_log_manager


class ClientApplication:
    """客户端应用程序"""

    def __init__(self, config_file: str = "config.json"):
        """初始化客户端应用"""
        self.config_file = config_file
        self.config = self.load_config()
        self.logger = self.setup_logging()

        # 组件
        self.server_client: Optional[ServerClient] = None
        self.web_server_task: Optional[asyncio.Task] = None
        self.server_client_task: Optional[asyncio.Task] = None

        # 运行状态
        self.running = False
        self.shutdown_event = asyncio.Event()

        # 设置信号处理
        self.setup_signal_handlers()

    def load_config(self) -> dict:
        """加载配置（优先使用环境变量）"""
        # 从环境变量读取配置，如果没有则使用默认值
        config = {
            "server": {
                "host": os.getenv("SERVER_HOST", "*************"),
                "port": int(os.getenv("SERVER_PORT", "8888"))
            },
            "device": {
                "id": os.getenv("DEVICE_ID", "RK3588_001"),
                "web_port": int(os.getenv("WEB_PORT", "7001"))
            },
            "udp": {
                "target_ip": os.getenv("UDP_TARGET_IP", "************"),
                "target_port": int(os.getenv("UDP_TARGET_PORT", "8080")),
                "listen_port": int(os.getenv("UDP_PORT", "18001"))
            }
        }

        print("配置加载成功（使用环境变量）:")
        print(f"  服务器: {config['server']['host']}:{config['server']['port']}")
        print(f"  设备ID: {config['device']['id']}")
        print(f"  Web端口: {config['device']['web_port']}")
        print(f"  下位机: {config['udp']['target_ip']}:{config['udp']['target_port']}")
        print(f"  UDP端口: {config['udp']['listen_port']}")

        return config

    def setup_logging(self) -> logging.Logger:
        """设置日志"""
        # 使用简化的日志管理器，配置从环境变量读取
        log_manager = setup_logging(
            log_file="logs/client_operations.log"
        )

        logger = logging.getLogger(__name__)
        logger.info("简化日志系统初始化完成（支持自动轮转和清理）")
        return logger

    def setup_signal_handlers(self):
        """设置信号处理器"""
        def signal_handler(signum, frame):
            self.logger.info(f"收到信号 {signum}，开始关闭程序...")
            asyncio.create_task(self.shutdown())

        # 注册信号处理器
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)

    async def start_web_server(self):
        """启动Web服务器"""
        try:
            self.logger.info("启动Web服务器...")
            await start_web_server(self.config_file)
        except Exception as e:
            self.logger.error(f"Web服务器运行错误: {e}")
            await self.shutdown()

    async def start_server_client(self):
        """启动服务器客户端"""
        try:
            self.logger.info("启动服务器客户端...")
            self.server_client = ServerClient(self.config_file)
            # 设置全局引用，让web_server可以访问
            set_global_server_client(self.server_client)
            await self.server_client.start()
        except Exception as e:
            self.logger.error(f"服务器客户端运行错误: {e}")
            await self.shutdown()

    async def health_monitor(self):
        """健康监控"""
        while self.running:
            try:
                # 检查各组件状态
                web_server_alive = self.web_server_task and not self.web_server_task.done()
                server_client_alive = self.server_client_task and not self.server_client_task.done()

                if not web_server_alive:
                    self.logger.warning("Web服务器任务已停止")

                if not server_client_alive:
                    self.logger.warning("服务器客户端任务已停止")

                # 记录状态信息
                if self.server_client:
                    status = self.server_client.get_status()
                    self.logger.debug(f"服务器连接状态: {status}")

                await asyncio.sleep(60)  # 每分钟检查一次

            except Exception as e:
                self.logger.error(f"健康监控错误: {e}")
                await asyncio.sleep(10)

    async def start(self):
        """启动客户端应用"""
        self.running = True
        self.logger.info("=" * 50)
        self.logger.info("RK3588客户端应用启动")
        self.logger.info(f"设备ID: {self.config['device']['id']}")
        self.logger.info(f"Web端口: {self.config['device']['web_port']}")
        self.logger.info(f"服务器: {self.config['server']['host']}:{self.config['server']['port']}")
        self.logger.info("=" * 50)

        # 记录客户端启动到日志
        log_manager = get_log_manager()
        log_manager.log_client_startup(
            device_id=self.config['device']['id'],
            web_port=self.config['device']['web_port']
        )

        try:
            # 创建任务
            self.web_server_task = asyncio.create_task(
                self.start_web_server(),
                name="web_server"
            )

            self.server_client_task = asyncio.create_task(
                self.start_server_client(),
                name="server_client"
            )

            health_monitor_task = asyncio.create_task(
                self.health_monitor(),
                name="health_monitor"
            )

            # 等待关闭信号或任务完成
            done, pending = await asyncio.wait(
                [
                    self.web_server_task,
                    self.server_client_task,
                    health_monitor_task,
                    asyncio.create_task(self.shutdown_event.wait())
                ],
                return_when=asyncio.FIRST_COMPLETED
            )

            # 取消未完成的任务
            for task in pending:
                task.cancel()
                try:
                    await task
                except asyncio.CancelledError:
                    pass

            self.logger.info("所有任务已完成")

        except Exception as e:
            self.logger.error(f"应用运行错误: {e}")
        finally:
            await self.cleanup()

    async def shutdown(self):
        """关闭应用"""
        if not self.running:
            return

        self.logger.info("开始关闭应用...")
        self.running = False
        self.shutdown_event.set()

        # 停止服务器客户端
        if self.server_client:
            await self.server_client.stop()

        self.logger.info("应用关闭完成")

    async def cleanup(self):
        """清理资源"""
        self.logger.info("清理资源...")

        # 确保所有任务都已取消
        tasks = [self.web_server_task, self.server_client_task]
        for task in tasks:
            if task and not task.done():
                task.cancel()
                try:
                    await task
                except asyncio.CancelledError:
                    pass

        self.logger.info("资源清理完成")


async def main():
    """主函数"""
    print("RK3588客户端启动中...")

    # 检查配置文件
    config_file = "config.json"
    if not os.path.exists(config_file):
        print(f"配置文件 {config_file} 不存在，将创建默认配置")

    # 创建并启动应用
    app = ClientApplication(config_file)

    try:
        await app.start()
    except KeyboardInterrupt:
        print("\n用户中断程序")
    except Exception as e:
        print(f"程序运行错误: {e}")
        import traceback
        traceback.print_exc()
    finally:
        print("程序退出")


if __name__ == "__main__":
    # 设置事件循环策略（Linux下可能需要）
    if sys.platform.startswith('linux'):
        try:
            import uvloop
            asyncio.set_event_loop_policy(uvloop.EventLoopPolicy())
        except ImportError:
            pass

    # 运行主程序
    asyncio.run(main())
