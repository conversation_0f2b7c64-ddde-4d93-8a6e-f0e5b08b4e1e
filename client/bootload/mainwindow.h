#ifndef MAINWINDOW_H
#define MAINWINDOW_H

#include <QMainWindow>
#include <QCloseEvent>
#include "version.h"

// 前向声明
class CANDownload;
class EthernetDownload;

QT_BEGIN_NAMESPACE
namespace Ui { class MainWindow; }
QT_END_NAMESPACE

class MainWindow : public QMainWindow
{
    Q_OBJECT

public:
    MainWindow(QWidget *parent = nullptr);
    ~MainWindow();

protected:
    // 添加关闭事件处理函数
    void closeEvent(QCloseEvent *event) override;

private slots:
    void on_switchButton_clicked();

private:
    Ui::MainWindow *ui;
    CANDownload *m_canDownload;
    EthernetDownload *m_ethernetDownload;
    bool m_isCANMode;  // true 表示当前是 CAN 下载模式

    // 关闭当前打开的窗口并释放资源
    void closeCurrentWindow();
};

#endif // MAINWINDOW_H 