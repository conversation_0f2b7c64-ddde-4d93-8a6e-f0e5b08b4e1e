# bootload

#### 开发日志

V1.01

初始版本

V1.02

增加显示版本号，这样可以知道是否打包成功

V1.03

增加文件目录保存功能，下一次点击下载弹出对话框时自动定位到上一次的目录

增加listWidget框的信息自动保存成txt文件，保存在应用程序运行目录下的log文件夹，文件名为时间戳+log，debug信息也要保存成文件，文件名为时间戳+debug

V1.04

解决点击下载会闪退的问题

V1.05

前面版本发现下载后没有回应会闪退，难道还有跨线程操作UI的？

想起关闭CAN卡确实会CloseCAN

V1.06

发现连接按钮按下后没有被设置为断开

V1.07

StartCAN中的ui操作也加入队列，由主进程操作ui

V1.08

需要判断list控件内容，大于一定数值就要清空，避免程序卡死

滑动条要可以上拉查看list的内容，上拉后不要刷新list，按下回车后重新开始刷新list

