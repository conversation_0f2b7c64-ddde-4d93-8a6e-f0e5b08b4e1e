#include "ethernetdownload.h"
#include "ui_ethernetdownload.h"
#include <QtWidgets/QFileDialog>
#include <QtCore/QDebug>
#include <QtGui/QRegExpValidator>
#include <QtConcurrent/QtConcurrent>
#include <QtCore/QThread>
#include <QtGui/QValidator>
#include <QtCore/QTimer>
#include <QtCore/QEventLoop>
#include <QtCore/QElapsedTimer>
#include <QtWidgets/QScrollBar>
#include <QtGui/QKeyEvent>
#include <QtWidgets/QStatusBar>  
#include <QtWidgets/QPushButton>  
#include <QtWidgets/QMenu>
#include <QtNetwork/QTcpSocket>
#include <QtNetwork/QHostAddress>
#include <QtCore/QCoreApplication>
#include <QtCore/QSettings>
#include <QtCore/QDateTime>
#include <QtCore/QDir>
#include <QtCore/QFileInfo>
#include <QtNetwork/QNetworkInterface>
#include <QtCore/QProcess>
#include <QtGui/QClipboard>
#include <QtWidgets/QApplication>

// 使用 Common 命名空间
using namespace Common;

EthernetDownload::EthernetDownload(QWidget *parent)
    : QMainWindow(parent), ui(new Ui::EthernetDownload), m_autoScroll(true), m_socket(nullptr)
{
    ui->setupUi(this);
    
    // 设置应用程序图标
    setWindowIcon(QIcon(":/images/icon.png"));
    

    // 这些lambda表达式的连接需要保留，因为它们不是标准的on_objectName_signalName格式
    connect(ui->localIP, &QComboBox::currentTextChanged, this, [this](const QString &text) {
        m_localIP = m_networkInterfaces.value(text, text);
    });
    
    connect(ui->targetIP, &QLineEdit::textChanged, this, [this](const QString &text) {
        QRegExp ipRegex("^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$");
        if (ipRegex.exactMatch(text)) {
            m_targetIP = text;
            addListMessage(QString("目标IP已更改为: %1").arg(text));
        }
    });
    
    // 调用初始化设置函数
    initializeSettings();
    initializeNetworkInterfaces();
    addListMessage(QString("当前上位机版本：%1").arg(APP_VERSION));  // 使用统一的版本号

    // 禁用所有相关的按钮
    ui->Download->setEnabled(false);
    ui->ClearFlag->setEnabled(false);
    ui->Connect->setEnabled(true);

    // 初始化网络相关变量
    m_connect = 0;           // 0,未连接
    
    // 设置默认端口
    ui->localPort->setText("7811");
    ui->targetPort->setText("7812");
    m_localPort = 7811;
    m_targetPort = 7812;
    
    // 设置IP地址验证器
    QRegExp ipRegex("^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$");
    QValidator *ipValidator = new QRegExpValidator(ipRegex, this);
    ui->targetIP->setValidator(ipValidator);
    ui->paraTargetIPEdit->setValidator(ipValidator);
    
    // 设置端口验证器
    QIntValidator *portValidator = new QIntValidator(1, 65535, this);
    ui->localPort->setValidator(portValidator);
    ui->targetPort->setValidator(portValidator);

    CodeAreaSel(ui->CodeArea->currentText());

    m_timer = new QTimer(this);
    m_timer->setInterval(500);
    // 连接定时器的超时信号到 lambda 函数
    connect(m_timer, &QTimer::timeout, this, [this]()
    {
        this->FeedDog();
    });

    // 连接滚动条信号到槽函数
    connect(ui->listWidget->verticalScrollBar(), &QScrollBar::valueChanged, this, &EthernetDownload::onListWidgetScrolled);

    // 设置列表小部件的上下文菜单策略
    ui->listWidget->setContextMenuPolicy(Qt::CustomContextMenu);
    connect(ui->listWidget, &QWidget::customContextMenuRequested, this, &EthernetDownload::showListContextMenu);

    // 创建并初始化socket
    m_socket = new QUdpSocket(this);
    connect(m_socket, &QUdpSocket::readyRead, this, &EthernetDownload::handleSocketReadyRead);
    connect(m_socket, QOverload<QAbstractSocket::SocketError>::of(&QAbstractSocket::error),
            this, &EthernetDownload::handleSocketError);

    // 使用新的函数名
    connect(ui->paraTargetIPModify, &QPushButton::clicked, this, &EthernetDownload::handleParaTargetIPModify);

    // 设置参数IP编辑框的默认值
    ui->paraTargetIPEdit->setText("************");
}

EthernetDownload::~EthernetDownload()
{
    CloseNetwork();
    qDebug() << "关闭连接，程序退出";

    // 设置配置文件路径为应用程序运行目录下的config文件夹
    QString configDirPath = QCoreApplication::applicationDirPath() + "/config";
    QString settingsFile = configDirPath + "/settings.ini";
    QSettings settings(settingsFile, QSettings::IniFormat);

    // 保存当前目录到配置文件
    settings.setValue("lastDirectory", m_lastDirectory);

    if (m_socket) {
        m_socket->close();
        delete m_socket;
    }

    delete ui;
}

// 网络通信相关的槽函数
void EthernetDownload::handleSocketError(QAbstractSocket::SocketError socketError)
{
    QString errorMsg;
    switch (socketError) {
        case QAbstractSocket::ConnectionRefusedError:
            errorMsg = "连接被拒绝";
            break;
        case QAbstractSocket::RemoteHostClosedError:
            errorMsg = "远程主机关闭连接";
            break;
        case QAbstractSocket::HostNotFoundError:
            errorMsg = "找不到主机";
            break;
        case QAbstractSocket::SocketTimeoutError:
            errorMsg = "连接超时";
            break;
        default:
            errorMsg = m_socket->errorString();
            break;
    }
    addListMessage(QString("网络错误: %1").arg(errorMsg));
    CloseNetwork();
}

void EthernetDownload::handleSocketReadyRead()
{
    while (m_socket->hasPendingDatagrams()) {
        QByteArray datagram;
        datagram.resize(m_socket->pendingDatagramSize());
        QHostAddress sender;
        quint16 senderPort;

        m_socket->readDatagram(datagram.data(), datagram.size(), &sender, &senderPort);
        
        // 添加调试信息
        QString hexData;
        for (char byte : datagram) {
            hexData += QString("%1 ").arg(static_cast<uint8_t>(byte), 2, 16, QLatin1Char('0')).toUpper();
        }
        addListMessage(QString("收到数据: %1 从 %2:%3").arg(hexData).arg(sender.toString()).arg(senderPort));
        
        m_receiveBuffer.append(datagram);
    }
}

void EthernetDownload::showListContextMenu(const QPoint &pos)
{
    QMenu contextMenu(tr("列表菜单"), this);

    // 添加全选动作
    QAction selectAllAction("全选并复制", this);
    connect(&selectAllAction, &QAction::triggered, this, [this]() {
        ui->listWidget->selectAll();
        QStringList selectedTexts;
        for (int i = 0; i < ui->listWidget->count(); ++i) {
            selectedTexts.append(ui->listWidget->item(i)->text());
        }
        if (!selectedTexts.isEmpty()) {
            QApplication::clipboard()->setText(selectedTexts.join("\n"));
            addListMessage("已复制所有日志消息到剪贴板");
        }
    });
    selectAllAction.setEnabled(ui->listWidget->count() > 0);
    contextMenu.addAction(&selectAllAction);

    // 添加复制动作
    QAction copyAction("复制选中", this);
    connect(&copyAction, &QAction::triggered, this, &EthernetDownload::copySelectedMessages);
    copyAction.setEnabled(ui->listWidget->selectedItems().count() > 0);
    contextMenu.addAction(&copyAction);

    // 添加清理动作
    QAction clearAction("清理所有消息", this);
    connect(&clearAction, &QAction::triggered, this, &EthernetDownload::clearAllMessages);
    clearAction.setEnabled(ui->listWidget->count() > 0);
    contextMenu.addAction(&clearAction);

    // 显示菜单
    contextMenu.exec(ui->listWidget->mapToGlobal(pos));
}

void EthernetDownload::selectAllMessages()
{
    ui->listWidget->selectAll();
    copySelectedMessages(); // 全选后立即复制
}

void EthernetDownload::copySelectedMessages()
{
    QStringList selectedTexts;
    for (QListWidgetItem* item : ui->listWidget->selectedItems()) {
        selectedTexts.append(item->text());
    }
    
    if (!selectedTexts.isEmpty()) {
        QApplication::clipboard()->setText(selectedTexts.join("\n"));
        addListMessage("已复制选中的消息到剪贴板");
    }
}

void EthernetDownload::clearAllMessages()
{
    ui->listWidget->clear();
    m_pendingMessages.clear(); // 清理待处理的消息
    addListMessage("所有消息已清理");
    // 重置滚动状态
    m_autoScroll = true;
}

void EthernetDownload::initializeSettings()
{
    // 设置配置文件路径为应用程序运行目录下的config文件夹
    QString configDirPath = QCoreApplication::applicationDirPath() + "/config";
    QDir configDir(configDirPath);

    // 如果config文件夹不存在，则创建
    if (!configDir.exists())
    {
        configDir.mkpath(".");
    }

    // 指定配置文件路径
    QString settingsFile = configDirPath + "/settings.ini";
    QSettings settings(settingsFile, QSettings::IniFormat);

    // 加载保存的目录
    m_lastDirectory = settings.value("lastDirectory", QDir::homePath()).toString();

    // 初始化日志文件
    initializeLogFiles();
}


void EthernetDownload::initializeLogFiles()
{
    // 获取应用程序运行目录
    QString appDirPath = QCoreApplication::applicationDirPath();

    // 创建log文件夹路径
    QString logDirPath = appDirPath + "/log";
    QDir logDir(logDirPath);

    // 如果log文件夹不存在，则创建
    if (!logDir.exists())
    {
        logDir.mkpath(".");
    }

    // 创建日志文件名和路径
    QString timestamp = QDateTime::currentDateTime().toString("yyyyMMdd_hhmmss");
    m_logFilePath = logDirPath + "/" + timestamp + "_log.txt";
}



void EthernetDownload::TaskA()
{
    // 发送数据并等待响应
    if (!WriteFlag())
    {
        return;
    }

    QMetaObject::invokeMethod(m_timer, "start", Qt::QueuedConnection);//跨线程启动定时器

    if(!ReadVersion(0x1))
    {
        return;
    }
    if(!ReadVersion(0x2))
    {
        return;
    }
    if(!ReadVersion(0x3))
    {
        return;
    }

    // 添加读取PSN功能
    if(!ReadPSN(0x01))  // 读取BOOT0区域PSN
    {
        return;
    }
    if(!ReadPSN(0x02))  // 读取BOOT1区域PSN
    {
        return;
    }
}

void EthernetDownload::TaskB()
{
    // 发送选择的bin文件
    if (!SendDataOverNetwork())
    {
        return;
    }
    //写版本
    if (!WriteVersion())
    {
        return;
    }
    //测试运行
    if (!TestRun())
    {
        return;
    }
}

void EthernetDownload::FeedDog()
{
    QByteArray command;
    command.append(static_cast<char>(0x02)); // 命令类型：喂狗
    command.append(static_cast<char>(0x00)); // 子命令

    // 发送喂狗命令（目标端口号为本地端口号加2）
    qint64 written = m_socket->writeDatagram(command, QHostAddress(m_targetIP), m_localPort + 2);
    if (written != command.size()) {
        addListMessage("喂狗命令发送失败!");
    } else {
        addListMessage("喂狗命令发送成功...");
    }
}


bool EthernetDownload::ReadVersion(uint8_t region)
{
    // 清空接收缓冲区
    m_receiveBuffer.clear();

    QByteArray command;
    command.append(static_cast<char>(0x01)); // 命令类型：读取版本
    command.append(static_cast<char>(0x00)); // 子命令
    command.append(static_cast<char>(region)); // 区域标识

    if (!SendCommand(command)) {
        addListMessage("读取版本号命令发送失败!");
        CloseNetwork();
        return false;
    }

    addListMessage(QString("读取版本号命令已发送，区域: %1").arg(region, 2, 16, QLatin1Char('0')).toUpper());

    QElapsedTimer timer;
    timer.start();
    const int timeout = 5000;

    while (timer.elapsed() < timeout) {
        if (!m_receiveBuffer.isEmpty()) {
            QByteArray response = m_receiveBuffer;
            m_receiveBuffer.clear();

            if (response.size() >= 8) {
                uint8_t cmd = static_cast<uint8_t>(response[0]);
                uint8_t subcmd = static_cast<uint8_t>(response[1]);
                uint8_t resp_region = static_cast<uint8_t>(response[2]);
                
                if (cmd == 0x01 && subcmd == 0x00 && resp_region == region) {
                    // 重新调整版本号解析逻辑
                    uint8_t yearHigh = static_cast<uint8_t>(response[3]); // 20
                    uint8_t yearLow = static_cast<uint8_t>(response[4]);  // 24
                    uint8_t month = static_cast<uint8_t>(response[5]);    // 01
                    uint8_t day = static_cast<uint8_t>(response[6]);      // 10
                    uint8_t version = static_cast<uint8_t>(response[7]);  // 03

                    QString version_str = QString("区域: %1, 日期: %2%3-%4-%5, 版本号: %6")
                        .arg(resp_region, 2, 16, QLatin1Char('0'))  // 区域仍用十六进制
                        .arg(yearHigh, 2, 10, QLatin1Char('0'))     // 年份高位(20)
                        .arg(yearLow, 2, 10, QLatin1Char('0'))      // 年份低位(24)
                        .arg(month, 2, 10, QLatin1Char('0'))        // 月
                        .arg(day, 2, 10, QLatin1Char('0'))          // 日
                        .arg(version, 2, 16, QLatin1Char('0'));     // 版本号用十六进制

                    addListMessage(QString("版本信息接收成功: %1").arg(version_str));
                    return true;
                }
                else {
                    addListMessage(QString("收到无效响应: cmd=%1, subcmd=%2, region=%3")
                        .arg(cmd, 2, 16, QLatin1Char('0'))
                        .arg(subcmd, 2, 16, QLatin1Char('0'))
                        .arg(resp_region, 2, 16, QLatin1Char('0')));
                }
            }
        }
        QCoreApplication::processEvents();
        QThread::msleep(10);  // 添加短暂延时，减少CPU占用
    }

    addListMessage("版本信息接收超时...");
    CloseNetwork();
    return false;
}


bool EthernetDownload::WriteVersion()
{
    if (m_fileName.isEmpty()) {
        addListMessage("文件名未保存，无法执行版本号写入！");
        CloseNetwork();
        return false;
    }

    QFileInfo fileInfo(m_fileName);
    QString baseName = fileInfo.baseName();

    if (baseName.length() != 10) {
        addListMessage("文件名格式不正确，无法提取版本信息！");
        CloseNetwork();
        return false;
    }

    QString yearHighStr = baseName.mid(0, 2);
    QString yearLowStr = baseName.mid(2, 2);
    QString monthStr = baseName.mid(4, 2);
    QString dayStr = baseName.mid(6, 2);
    QString versionStr = baseName.mid(8, 2);

    uint8_t yearHigh = static_cast<uint8_t>(yearHighStr.toInt());
    uint8_t yearLow = static_cast<uint8_t>(yearLowStr.toInt());
    uint8_t month = static_cast<uint8_t>(monthStr.toInt());
    uint8_t day = static_cast<uint8_t>(dayStr.toInt());
    uint8_t version = static_cast<uint8_t>(versionStr.toInt(nullptr, 16));

    QByteArray command;
    command.append(static_cast<char>(0x01)); // 命令类型
    command.append(static_cast<char>(0x03)); // 子命令
    command.append(static_cast<char>(m_CodeArea)); // 区域ID
    command.append(static_cast<char>(yearHigh)); // 年份高字节
    command.append(static_cast<char>(yearLow)); // 年份低字节
    command.append(static_cast<char>(month)); // 月份
    command.append(static_cast<char>(day)); // 日期
    command.append(static_cast<char>(version)); // 版本号

    m_receiveBuffer.clear();  // 清空接收缓冲区
    if (!SendCommand(command)) {
        addListMessage("版本号写入命令发送失败！");
        CloseNetwork();
        return false;
    }

    addListMessage("版本号写入命令发送成功，等待从EEPROM读取并验证版本号...");

    QElapsedTimer timer;
    timer.start();
    const int timeout = 5000;

    while (timer.elapsed() < timeout) {
        if (!m_receiveBuffer.isEmpty()) {
            QByteArray response = m_receiveBuffer;
            m_receiveBuffer.clear();

            if (response.size() >= 8) {
                uint8_t respArea = static_cast<uint8_t>(response[2]);
                if (respArea == static_cast<uint8_t>(m_CodeArea) &&
                    static_cast<uint8_t>(response[3]) == yearHigh &&
                    static_cast<uint8_t>(response[4]) == yearLow &&
                    static_cast<uint8_t>(response[5]) == month &&
                    static_cast<uint8_t>(response[6]) == day &&
                    static_cast<uint8_t>(response[7]) == version) {
                    addListMessage("版本号写入成功并验证通过！");
                    return true;
                }
            }
        }
        QCoreApplication::processEvents();
        QThread::msleep(10);
    }

    addListMessage("版本号写入超时或验证失败！");
    CloseNetwork();
    return false;
}


bool EthernetDownload::WriteFlag()
{
    QByteArray command;
    command.append(static_cast<char>(0x00)); // 命令类型
    command.append(static_cast<char>(0x01)); // 子命令：标志位置1

    m_receiveBuffer.clear();  // 清空接收缓冲区
    if (!SendCommand(command)) {
        addListMessage("标志位置1命令发送失败!");
        CloseNetwork();
        return false;
    }

    addListMessage("标志位置1命令发送成功,等待重启进入BOOT程序!");

    QElapsedTimer timer;
    timer.start();
    const int timeout = 5000;

    while (timer.elapsed() < timeout) {
        if (!m_receiveBuffer.isEmpty()) {  // 使用缓冲区而不是直接读取
            QByteArray response = m_receiveBuffer;
            m_receiveBuffer.clear();

            if (response.size() >= 2) {
                uint8_t cmd = static_cast<uint8_t>(response[0]);
                uint8_t subcmd = static_cast<uint8_t>(response[1]);
                if (cmd == 0x00 && subcmd == 0x01) {
                    addListMessage("ECU已经重启并进入BOOT!");
                    return true;
                }
            }
        }
        QCoreApplication::processEvents();
        QThread::msleep(10);
    }

    addListMessage("标志位置1命令响应接收超时...");
    CloseNetwork();
    return false;
}

bool EthernetDownload::TestRun()
{
    m_receiveBuffer.clear();  // 清空接收缓冲区
    QByteArray command;
    command.append(static_cast<char>(0x01)); // 命令类型
    command.append(static_cast<char>(0x02)); // 子命令：测试运行

    if (!SendCommand(command)) {
        addListMessage("测试运行命令发送失败!");
        CloseNetwork();
        return false;
    }

    addListMessage("测试运行命令发送成功!");

    QElapsedTimer timer;
    timer.start();
    const int timeout = 5000;

    while (timer.elapsed() < timeout) {
        if (!m_receiveBuffer.isEmpty()) {
            QByteArray response = m_receiveBuffer;
            m_receiveBuffer.clear();

            if (response.size() >= 2) {
                uint8_t cmd = static_cast<uint8_t>(response[0]);
                uint8_t subcmd = static_cast<uint8_t>(response[1]);
                if (cmd == 0x01 && subcmd == 0x02) {
                    addListMessage("ECU已经 跳转（APP） / 写PSN并重启（BOOT）!");
                    return true;
                }
            }
        }
        QCoreApplication::processEvents();
        QThread::msleep(10);
    }

    addListMessage("测试运行命令响应接收超时...");
    CloseNetwork();
    return false;
}



void EthernetDownload::CodeAreaSel(const QString &arg1)
{
    if (arg1 == "BOOT0") {
        m_CodeArea = 1;
    }
    else if (arg1 == "BOOT1") {
        m_CodeArea = 2;
    }
    else if (arg1 == "APP") {
        m_CodeArea = 3;
    }
    else {
        qDebug() << "未知编程区域";
        return;
    }
    QString message = QString("当前编程区域为: %1").arg(arg1);
    addListMessage(message);
}

void EthernetDownload::on_CodeArea_currentTextChanged(const QString &arg1)
{
    qDebug() << "CodeArea:" << arg1;
    CodeAreaSel(arg1);
}

void EthernetDownload::addListMessage(const QString &message)
{
    if (QThread::currentThread() != qApp->thread()) {
        // 如果不在主线程，则通过信号槽机制发送到主线程
        QMetaObject::invokeMethod(this, "addListMessage",
                                Qt::QueuedConnection,
                                Q_ARG(QString, message));
        return;
    }

    QString timestamp = QDateTime::currentDateTime().toString("[yyyy-MM-dd hh:mm:ss] ");
    QString logEntry = timestamp + message;

    QScrollBar *scrollBar = ui->listWidget->verticalScrollBar();
    bool wasAtBottom = scrollBar->value() == scrollBar->maximum();

    if (wasAtBottom) {
        checkAndCleanListWidget();
        ui->listWidget->addItem(logEntry);
        QTimer::singleShot(0, scrollBar, [scrollBar]() {
            scrollBar->setValue(scrollBar->maximum());
        });
    } else {
        m_pendingMessages.append(logEntry);
    }

    // 将日志条目保存到文件
    QFile logFile(m_logFilePath);
    if (logFile.open(QIODevice::Append | QIODevice::Text)) {
        QTextStream out(&logFile);
        out << logEntry << "\n";
        logFile.close();
    }
}

void EthernetDownload::checkAndCleanListWidget()
{
    const int maxItems = 500; // 设置最大列表项数量
    int currentCount = ui->listWidget->count();
    if (currentCount > maxItems) {
        int itemsToRemove = currentCount - maxItems;
        
        QScrollBar *scrollBar = ui->listWidget->verticalScrollBar();
        bool wasAtBottom = scrollBar->value() == scrollBar->maximum();
        
        ui->listWidget->setUpdatesEnabled(false);
        
        for (int i = 0; i < itemsToRemove; ++i) {
            delete ui->listWidget->takeItem(0);
        }
        
        ui->listWidget->setUpdatesEnabled(true);
        
        if (wasAtBottom || m_autoScroll) {
            QTimer::singleShot(0, this, [this]() {
                ui->listWidget->scrollToBottom();
                qDebug() << "Scrolled to bottom after cleaning";
            });
        } else {
            int newScrollValue = qMin(scrollBar->value(), scrollBar->maximum());
            scrollBar->setValue(newScrollValue);
            qDebug() << "Restored scroll position after cleaning:" << newScrollValue;
        }
        
        qDebug() << "列表清理完成,当前项目数:" << ui->listWidget->count();
    }
}


void EthernetDownload::onListWidgetScrolled()
{
    QScrollBar *scrollBar = ui->listWidget->verticalScrollBar();
    bool isAtBottom = (scrollBar->value() >= scrollBar->maximum() - 5); // 允许一些误差

    if (isAtBottom && !m_pendingMessages.isEmpty()) {
        // 更新列表视图
        ui->listWidget->addItems(m_pendingMessages);
        m_pendingMessages.clear();
        
        // 确保滚动到底部
        QTimer::singleShot(0, scrollBar, [scrollBar]() {
            scrollBar->setValue(scrollBar->maximum());
        });
        
        qDebug() << "Updated list view with pending messages";
    }

    if (isAtBottom != m_autoScroll) {
        m_autoScroll = isAtBottom;
        qDebug() << "Auto-scroll state changed. Now:" << m_autoScroll;
    }
}

void EthernetDownload::keyPressEvent(QKeyEvent *event)
{
    if (event->key() == Qt::Key_Return || event->key() == Qt::Key_Enter)
    {
        // 按下回车键时恢复自动滚动
        m_autoScroll = true;
        ui->listWidget->scrollToBottom();
        qDebug() << "Enter key pressed, restoring auto-scroll";
    }
    else
    {
        QMainWindow::keyPressEvent(event);
    }
}




void EthernetDownload::OpenFile()
{
    QString filter = "Binary files (*.bin)"; // 定义文件过滤器

    // 使用上一次保存的目录
    QString fileName = QFileDialog::getOpenFileName(this, tr("打开文件"), m_lastDirectory, filter);

    if (!fileName.isEmpty())
    {
        // 保存当前目录
        QFileInfo fileInfo(fileName);
        m_lastDirectory = fileInfo.absolutePath();

        // 输出文件名
        addListMessage(QString("打开的文件：%1").arg(fileName));

        QString baseName = fileInfo.baseName(); // 获取文件名，不含扩展名

        QRegExp regExp("^\\d{4}\\d{2}\\d{2}[0-9A-F]{2}$"); // 例如: 20241230FF
        if (regExp.exactMatch(baseName))
        {
            addListMessage(QString("文件名格式正确: %1").arg(baseName));
            m_fileName = fileName; // 将完整的文件名（包括路径）保存到成员变量中

            QFile file(fileName);
            if (file.open(QIODevice::ReadOnly))
            {
                m_fileContent = file.readAll(); // 读取整个文件内容
                file.close();
                addListMessage(QString("文件内容读取成功，大小: %1 字节").arg(m_fileContent.size()));
            }
            else
            {
                addListMessage("文件读取失败");
                m_fileContent.clear(); // 读取失败时清空内容
            }
        }
        else
        {
            addListMessage(QString("文件名格式错误: %1，文件名应为年+月+日+小版本号，例如：20241230FF.bin").arg(baseName));
            m_fileName.clear(); // 如果格式不正确，清空成员变量
            m_fileContent.clear(); // 清空文件内容
        }
    }
}


uint16_t EthernetDownload::CalculateCRC(const QByteArray &data)
{
    static const uint16_t crc16tab[256] = {
        0x0000, 0x1021, 0x2042, 0x3063, 0x4084, 0x50a5, 0x60c6, 0x70e7,
        0x8108, 0x9129, 0xa14a, 0xb16b, 0xc18c, 0xd1ad, 0xe1ce, 0xf1ef,
        0x1231, 0x0210, 0x3273, 0x2252, 0x52b5, 0x4294, 0x72f7, 0x62d6,
        0x9339, 0x8318, 0xb37b, 0xa35a, 0xd3bd, 0xc39c, 0xf3ff, 0xe3de,
        0x2462, 0x3443, 0x0420, 0x1401, 0x64e6, 0x74c7, 0x44a4, 0x5485,
        0xa56a, 0xb54b, 0x8528, 0x9509, 0xe5ee, 0xf5cf, 0xc5ac, 0xd58d,
        0x3653, 0x2672, 0x1611, 0x0630, 0x76d7, 0x66f6, 0x5695, 0x46b4,
        0xb75b, 0xa77a, 0x9719, 0x8738, 0xf7df, 0xe7fe, 0xd79d, 0xc7bc,
        0x48c4, 0x58e5, 0x6886, 0x78a7, 0x0840, 0x1861, 0x2802, 0x3823,
        0xc9cc, 0xd9ed, 0xe98e, 0xf9af, 0x8948, 0x9969, 0xa90a, 0xb92b,
        0x5af5, 0x4ad4, 0x7ab7, 0x6a96, 0x1a71, 0x0a50, 0x3a33, 0x2a12,
        0xdbfd, 0xcbdc, 0xfbbf, 0xeb9e, 0x9b79, 0x8b58, 0xbb3b, 0xab1a,
        0x6ca6, 0x7c87, 0x4ce4, 0x5cc5, 0x2c22, 0x3c03, 0x0c60, 0x1c41,
        0xedae, 0xfd8f, 0xcdcc, 0xdded, 0xad2a, 0xbd0b, 0x8d68, 0x9d49,
        0x7e97, 0x6eb6, 0x5ed5, 0x4ef4, 0x3e13, 0x2e32, 0x1e51, 0x0e70,
        0xff9f, 0xefbe, 0xdfdd, 0xcffc, 0xbf1b, 0xaf3a, 0x9f59, 0x8f78,
        0x9188, 0x81a9, 0xb1ca, 0xa1eb, 0xd10c, 0xc12d, 0xf14e, 0xe16f,
        0x1080, 0x00a1, 0x30c2, 0x20e3, 0x5004, 0x4025, 0x7046, 0x6067,
        0x83b9, 0x9398, 0xa3fb, 0xb3da, 0xc33d, 0xd31c, 0xe37f, 0xf35e,
        0x02b1, 0x1290, 0x22f3, 0x32d2, 0x4235, 0x5214, 0x6277, 0x7256,
        0xb5ea, 0xa5cb, 0x95a8, 0x8589, 0xf56e, 0xe54f, 0xd52c, 0xc50d,
        0x34e2, 0x24c3, 0x14a0, 0x0481, 0x7466, 0x6447, 0x5424, 0x4405,
        0xa7db, 0xb7fa, 0x8799, 0x97b8, 0xe75f, 0xf77e, 0xc71d, 0xd73c,
        0x26d3, 0x36f2, 0x0691, 0x16b0, 0x6657, 0x7676, 0x4615, 0x5634,
        0xd94c, 0xc96d, 0xf90e, 0xe92f, 0x99c8, 0x89e9, 0xb98a, 0xa9ab,
        0x5844, 0x4865, 0x7806, 0x6827, 0x18c0, 0x08e1, 0x3882, 0x28a3,
        0xcb7d, 0xdb5c, 0xeb3f, 0xfb1e, 0x8bf9, 0x9bd8, 0xabbb, 0xbb9a,
        0x4a75, 0x5a54, 0x6a37, 0x7a16, 0x0af1, 0x1ad0, 0x2ab3, 0x3a92,
        0xfd2e, 0xed0f, 0xdd6c, 0xcd4d, 0xbdaa, 0xad8b, 0x9de8, 0x8dc9,
        0x7c26, 0x6c07, 0x5c64, 0x4c45, 0x3ca2, 0x2c83, 0x1ce0, 0x0cc1,
        0xef1f, 0xff3e, 0xcf5d, 0xdf7c, 0xaf9b, 0xbfba, 0x8fd9, 0x9ff8,
        0x6e17, 0x7e36, 0x4e55, 0x5e74, 0x2e93, 0x3eb2, 0x0ed1, 0x1ef0
    };
    uint16_t crc = 0;  // 使用相同的初始值

    for (int i = 0; i < data.size(); ++i)
    {
        crc = (crc << 8) ^ crc16tab[((crc >> 8) ^ static_cast<uint8_t>(data[i])) & 0xFF];
    }
    
    // 保留调试信息
    QString hexData;
    for (char byte : data) {
        hexData += QString("%1 ").arg(static_cast<uint8_t>(byte), 2, 16, QLatin1Char('0')).toUpper();
    }
    addListMessage(QString("计算CRC - 数据: %1, CRC: 0x%2")
        .arg(hexData)
        .arg(crc, 4, 16, QLatin1Char('0')).toUpper());
    
    return crc;
}


bool EthernetDownload::SendDataOverNetwork()
{
    const int chunkSize = 1024; // 每块的大小为1024字节
    int offset = 0;
    uint16_t cntChunk = 0;
    int totalChunks = (m_fileContent.size() + chunkSize - 1) / chunkSize; // 计算总块数
    QElapsedTimer timer;
    timer.start();

    while (offset < m_fileContent.size())
    {
        int bytesToSend = qMin(chunkSize, m_fileContent.size() - offset);
        QByteArray chunk = m_fileContent.mid(offset, bytesToSend);

        cntChunk++;
        if(cntChunk >= totalChunks) cntChunk = totalChunks;
        double progress = (static_cast<double>(cntChunk) / totalChunks) * 100.0;
        addListMessage(QString("下载进度： %1%").arg(progress, 0, 'f', 1));

        bool blockSent = false;
        while (!blockSent)
        {
            uint16_t crc = CalculateCRC(chunk);
            uint8_t region = m_CodeArea;

            // 添加调试信息
            addListMessage(QString("发送数据块 - 大小: %1, CRC: 0x%2, 区域: 0x%3")
                .arg(bytesToSend)
                .arg(crc, 4, 16, QLatin1Char('0'))
                .arg(region, 2, 16, QLatin1Char('0')).toUpper());

            // 构建帧头
            QByteArray header;
            header.append(static_cast<char>(0x01)); // 命令类型
            header.append(static_cast<char>(0x01)); // 子命令
            header.append(static_cast<char>((crc >> 8) & 0xFF)); // CRC高字节
            header.append(static_cast<char>(crc & 0xFF)); // CRC低字节
            header.append(static_cast<char>(region)); // 区域信息
            header.append(static_cast<char>((bytesToSend >> 16) & 0xFF)); // 长度高字节
            header.append(static_cast<char>((bytesToSend >> 8) & 0xFF)); // 长度中字节
            header.append(static_cast<char>(bytesToSend & 0xFF)); // 长度低字节

            if (!SendCommand(header)) {
                addListMessage("发送帧头失败!");
                CloseNetwork();
                return false;
            }

            // 2. 发送数据块
            if (!SendChunkOverNetwork(chunk)) {
                addListMessage("发送数据块失败!");
                CloseNetwork();
                return false;
            }

            // 3. 等待响应
            Common::CANResponseStatus responseStatus = WaitForDownLoadResponse();
            if (responseStatus == Common::ResponseSuccess) {
                blockSent = true;
            }
            else if (responseStatus == Common::ResponseRetry) {
                addListMessage("重发当前块...");
            }
            else {
                addListMessage("等待响应超时，终止发送!");
                CloseNetwork();
                return false;
            }
        }

        offset += bytesToSend;
    }

    cntChunk = 0;
    addListMessage(QString("文件全部发送完毕, 大小： %1 字节").arg(m_fileContent.size()));
    addListMessage(QString("花费时间： %1 秒").arg(timer.elapsed() / 1000.0, 0, 'f', 3));
    return true;
}

bool EthernetDownload::SendChunkOverNetwork(const QByteArray &chunk)
{
    const int frameSize = 8; // 每次发送8个字节
    int offset = 0;

    addListMessage("发送数据块。。。");
    while (offset < chunk.size())
    {
        int bytesToSend = qMin(frameSize, chunk.size() - offset);
        QByteArray frame = chunk.mid(offset, bytesToSend);

        if (!m_socket) {
            addListMessage("网络未初始化!");
            return false;
        }

        qint64 written = m_socket->writeDatagram(frame, QHostAddress(m_targetIP), m_targetPort);
        if (written != frame.size()) {
            addListMessage(QString("数据帧发送失败，偏移量: %1").arg(offset));
            return false;
        }

        offset += bytesToSend;
    }

    return true;
}

Common::CANResponseStatus EthernetDownload::WaitForDownLoadResponse()
{
    QElapsedTimer timer;
    timer.start();
    const int timeout = 5000;

    while (timer.elapsed() < timeout) {
        if (!m_receiveBuffer.isEmpty()) {
            QByteArray response = m_receiveBuffer;
            m_receiveBuffer.clear();

            if (response.size() >= 3) {
                uint8_t cmd = static_cast<uint8_t>(response[0]);
                uint8_t subcmd = static_cast<uint8_t>(response[1]);
                uint8_t responseType = static_cast<uint8_t>(response[2]);
                
                // 添加详细的响应信息
                addListMessage(QString("下载响应 - CMD: 0x%1, SubCMD: 0x%2, Type: 0x%3")
                    .arg(cmd, 2, 16, QLatin1Char('0'))
                    .arg(subcmd, 2, 16, QLatin1Char('0'))
                    .arg(responseType, 2, 16, QLatin1Char('0')).toUpper());

                if (cmd == 0x01 && subcmd == 0x01) {
                    if (responseType == 0x01) {
                        addListMessage("校验通过，继续发送下一块...");
                        return Common::ResponseSuccess;
                    }
                    else if (responseType == 0x02) {
                        addListMessage("校验不通过，重发当前块...");
                        return Common::ResponseRetry;
                    }
                    else {
                        addListMessage("校验码错误！");
                        return Common::ResponseTimeout;
                    }
                }
            }
        }
        QCoreApplication::processEvents();
        QThread::msleep(10);
    }

    addListMessage("等待响应超时...");
    return Common::ResponseTimeout;
}


bool EthernetDownload::ClearFlag()
{
    QByteArray command;
    command.append(static_cast<char>(0x00)); // 命令类型
    command.append(static_cast<char>(0x02)); // 子命令：标志位清0

    m_receiveBuffer.clear();  // 清空接收缓冲区
    if (!SendCommand(command)) {
        addListMessage("标志位清0命令发送失败!");
        CloseNetwork();
        return false;
    }

    addListMessage("标志位清0命令发送成功!");

    QElapsedTimer timer;
    timer.start();
    const int timeout = 8000;

    while (timer.elapsed() < timeout) {
        if (!m_receiveBuffer.isEmpty()) {  // 使用缓冲区而不是直接读取
            QByteArray response = m_receiveBuffer;
            m_receiveBuffer.clear();

            if (response.size() >= 2) {
                uint8_t cmd = static_cast<uint8_t>(response[0]);
                uint8_t subcmd = static_cast<uint8_t>(response[1]);
                if (cmd == 0x01 && subcmd == 0x02) {
                    addListMessage("标志位清0成功，ECU已经进入APP!");
                    QMetaObject::invokeMethod(m_timer, "stop", Qt::QueuedConnection);
                    return true;
                }
            }
        }
        QCoreApplication::processEvents();
        QThread::msleep(10);
    }

    addListMessage("标志位清0命令响应接收超时...");
    CloseNetwork();
    return false;
}


void EthernetDownload::on_Connect_clicked()
{
    // 防止按钮重复点击
    ui->Connect->setEnabled(false);
    ui->Download->setEnabled(false);
    ui->ClearFlag->setEnabled(false);
    QCoreApplication::processEvents();

    addListMessage("以太网连接按钮被点击");

    if (m_connect == 1) {
        addListMessage("已经处于连接状态，断开连接");
        CloseNetwork();
        return;
    }

    addListMessage("先确保关闭连接，然后再启动连接");
    CloseNetwork();
    StartNetwork();

    // 使用异步方式运行TaskA，但确保网络操作在主线程
    QTimer::singleShot(100, this, [this]() {
        // 在主线程中执行网络初始化
        QMetaObject::invokeMethod(this, [this]() {
            TaskA();
        }, Qt::QueuedConnection);
    });
}

void EthernetDownload::on_Download_clicked()
{
    addListMessage("下载按钮被点击");
    OpenFile();

    // 在主线程中执行网络操作
    QMetaObject::invokeMethod(this, [this]() {
        TaskB();
    }, Qt::QueuedConnection);
}

void EthernetDownload::on_ClearFlag_clicked()
{
    // 在主线程中执行网络操作
    QMetaObject::invokeMethod(this, [this]() {
        ClearFlag();
    }, Qt::QueuedConnection);
}

void EthernetDownload::testListCleaning()
{
    for (int i = 0; i < 1100; ++i) {
        addListMessage(QString("测试消息 %1").arg(i));
        QCoreApplication::processEvents();  // 处理待处理的事件，刷新UI
        QThread::msleep(1);  // 短暂延迟，使过程可见
    }
}

void EthernetDownload::StartNetwork()
{
    m_connect = 1; // 设置为连接状态

    // 更新网络配置
    m_localIP = m_networkInterfaces.value(ui->localIP->currentText());
    m_localPort = ui->localPort->text().toUShort();
    m_targetIP = ui->targetIP->text();
    m_targetPort = ui->targetPort->text().toUShort();

    addListMessage(QString("正在尝试连接... 本地: %1:%2 -> 目标: %3:%4")
        .arg(m_localIP)
        .arg(m_localPort)
        .arg(m_targetIP)
        .arg(m_targetPort));

    // 创建并初始化socket
    if (m_socket) {
        m_socket->close();
        delete m_socket;
    }
    m_socket = new QUdpSocket(this);
    
    // 添加调试信息
    connect(m_socket, &QUdpSocket::readyRead, this, [this]() {
        handleSocketReadyRead();
    });

    // 绑定本地地址和端口
    if (!m_socket->bind(QHostAddress(m_localIP), m_localPort)) {
        addListMessage(QString("绑定本地地址和端口失败! 错误: %1").arg(m_socket->errorString()));
        CloseNetwork();
        return;
    }
    addListMessage(QString("成功绑定本地端口: %1").arg(m_localPort));

    // UDP不需要建立连接，直接设置为已连接状态
    m_connect = 1;
    QMetaObject::invokeMethod(this, [this]()
    {
        ui->Connect->setText(tr("断开"));
        ui->Download->setEnabled(true);
        ui->ClearFlag->setEnabled(true);
        ui->Connect->setEnabled(true);
    }, Qt::QueuedConnection);

    addListMessage("UDP通信已准备就绪...");
    QCoreApplication::processEvents();
}

void EthernetDownload::CloseNetwork()
{
    QMetaObject::invokeMethod(m_timer, "stop", Qt::QueuedConnection);

    if (m_socket) {
        m_socket->close();
    }
    
    m_connect = 0;
    QThread::msleep(1000);

    addListMessage("连接已关闭");

    QMetaObject::invokeMethod(this, [this]() {
        ui->Connect->setText(tr("连接"));
        ui->Download->setEnabled(false);
        ui->ClearFlag->setEnabled(false);
        ui->Connect->setEnabled(true);
        QCoreApplication::processEvents();
    }, Qt::QueuedConnection);
}

bool EthernetDownload::SendCommand(const QByteArray &command)
{
    if (!m_socket) {
        addListMessage("网络未初始化!");
        return false;
    }

    // 修正逻辑运算符
    if (m_socket->state() != QAbstractSocket::BoundState) {
        if (!m_socket->bind(QHostAddress(m_localIP), m_localPort)) {
            addListMessage(QString("绑定本地地址和端口失败! 错误: %1").arg(m_socket->errorString()));
            return false;
        }
    }

    // 发送命令
    qint64 written = m_socket->writeDatagram(command, QHostAddress(m_targetIP), m_targetPort);
    if (written != command.size()) {
        addListMessage(QString("命令发送失败! 本地端口: %1").arg(m_socket->localPort()));
        return false;
    }
    
    addListMessage(QString("命令已从端口 %1 发送到 %2:%3")
        .arg(m_socket->localPort())
        .arg(m_targetIP)
        .arg(m_targetPort));
    
    return true;
}

bool EthernetDownload::WaitForResponse(const QByteArray &expectedResponse, int timeout)
{
    QElapsedTimer timer;
    timer.start();

    while (timer.elapsed() < timeout) {
        if (!m_receiveBuffer.isEmpty()) {  // 使用缓冲区而不是等待读取
            if (m_receiveBuffer.contains(expectedResponse)) {
                m_receiveBuffer.clear();
                return true;
            }
        }
        QCoreApplication::processEvents();
        QThread::msleep(10);
    }

    addListMessage("等待响应超时...");
    return false;
}

void EthernetDownload::initializeNetworkInterfaces()
{
    // 清空当前列表
    ui->localIP->clear();
    m_networkInterfaces.clear();

    // 获取所有网络接口
    QList<QNetworkInterface> interfaces = QNetworkInterface::allInterfaces();
    
    // 遍历所有网络接口
    for (const QNetworkInterface &interface : interfaces) {
        // 只获取活动的接口
        if (interface.flags().testFlag(QNetworkInterface::IsUp) && 
            interface.flags().testFlag(QNetworkInterface::IsRunning) &&
            !interface.flags().testFlag(QNetworkInterface::IsLoopBack)) {
            
            // 获取接口的IP地址列表
            QList<QNetworkAddressEntry> addresses = interface.addressEntries();
            
            for (const QNetworkAddressEntry &address : addresses) {
                QHostAddress ip = address.ip();
                // 只处理IPv4地址
                if (ip.protocol() == QAbstractSocket::IPv4Protocol) {
                    QString interfaceName = interface.humanReadableName();
                    QString ipAddress = ip.toString();
                    QString displayText = QString("%1 (%2)").arg(interfaceName, ipAddress);
                    
                    ui->localIP->addItem(displayText);
                    m_networkInterfaces[displayText] = ipAddress;
                }
            }
        }
    }

    // 如果没有找到任何网卡,添加本地回环地址
    if (ui->localIP->count() == 0) {
        QString loopback = "本地回环 (127.0.0.1)";
        ui->localIP->addItem(loopback);
        m_networkInterfaces[loopback] = "127.0.0.1";
    }

    // 选择第一个网卡并设置m_localIP
    if (ui->localIP->count() > 0) {
        ui->localIP->setCurrentIndex(0);
        m_localIP = m_networkInterfaces[ui->localIP->currentText()];
        
        // 根据本地IP设置目标IP默认值
        QStringList ipParts = m_localIP.split(".");
        if (ipParts.size() == 4) {
            // 保持前三段不变，最后一段设为20
            ipParts[3] = "20";
            QString defaultTargetIP = ipParts.join(".");
            ui->targetIP->setText(defaultTargetIP);
            m_targetIP = defaultTargetIP;
            addListMessage(QString("目标IP已设置为: %1").arg(defaultTargetIP));
        }
    }

    addListMessage(QString("当前选择的本地IP: %1").arg(m_localIP));
}

void EthernetDownload::handleParaTargetIPModify()
{
    addListMessage("参数修改按钮被点击");
    
    QString newIP = ui->paraTargetIPEdit->text().trimmed();
    addListMessage(QString("准备修改IP为: %1").arg(newIP));
    
    // 验证IP地址格式
    QRegExp ipRegex("^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$");
    if (!ipRegex.exactMatch(newIP)) {
        addListMessage("参数IP地址格式无效!");
        return;
    }

    if (ModifyParaTargetIP(newIP)) {
        addListMessage(QString("参数目标IP修改成功: %1").arg(newIP));
        ui->paraTargetIPEdit->clear();
    } else {
        addListMessage("参数目标IP修改失败!");
    }
}

bool EthernetDownload::ModifyParaTargetIP(const QString &newIP)
{
    addListMessage("开始执行参数IP修改");
    
    // 构建命令帧
    QByteArray command;
    command.append(static_cast<char>(0x05)); // 命令类型：修改参数
    command.append(static_cast<char>(0x01)); // 子命令：修改目标IP参数
    
    // 将IP地址转换为字节
    QStringList ipParts = newIP.split(".");
    for (const QString &part : ipParts) {
        command.append(static_cast<char>(part.toInt()));
    }

    QString cmdHex;
    for (char byte : command) {
        cmdHex += QString("%1 ").arg(static_cast<uint8_t>(byte), 2, 16, QLatin1Char('0')).toUpper();
    }
    addListMessage(QString("发送命令帧: %1").arg(cmdHex));

    m_receiveBuffer.clear();
    if (!SendCommand(command)) {
        addListMessage("发送修改参数IP命令失败!");
        return false;
    }

    addListMessage("命令发送成功，等待响应...");

    // 等待响应
    QElapsedTimer timer;
    timer.start();
    const int timeout = 5000;

    while (timer.elapsed() < timeout) {
        if (!m_receiveBuffer.isEmpty()) {
            QByteArray response = m_receiveBuffer;
            m_receiveBuffer.clear();

            QString respHex;
            for (char byte : response) {
                respHex += QString("%1 ").arg(static_cast<uint8_t>(byte), 2, 16, QLatin1Char('0')).toUpper();
            }
            addListMessage(QString("收到响应: %1").arg(respHex));

            if (response.size() >= 6) {  // 命令(1) + 子命令(1) + IP地址(4)
                uint8_t cmd = static_cast<uint8_t>(response[0]);
                uint8_t subcmd = static_cast<uint8_t>(response[1]);
                
                if (cmd == 0x05 && subcmd == 0x01) {
                    // 从响应中提取IP地址
                    QString receivedIP = QString("%1.%2.%3.%4")
                        .arg(static_cast<uint8_t>(response[2]))
                        .arg(static_cast<uint8_t>(response[3]))
                        .arg(static_cast<uint8_t>(response[4]))
                        .arg(static_cast<uint8_t>(response[5]));
                    
                    addListMessage(QString("收到的IP地址: %1").arg(receivedIP));
                    
                    // 验证接收到的IP与期望的IP是否一致
                    if (receivedIP == newIP) {
                        m_paraTargetIP = newIP;
                        addListMessage("参数修改成功，IP地址已验证!");
                        return true;
                    } else {
                        addListMessage(QString("IP地址验证失败! 期望: %1, 实际: %2").arg(newIP).arg(receivedIP));
                        return false;
                    }
                }
            }
        }
        QCoreApplication::processEvents();
        QThread::msleep(10);
    }

    addListMessage("修改参数IP命令响应超时...");
    return false;
}

bool EthernetDownload::ReadPSN(uint8_t region)
{
    // 清空接收缓冲区
    m_receiveBuffer.clear();

    QByteArray command;
    command.append(static_cast<char>(0x04));  // 命令类型：读取PSN
    command.append(static_cast<char>(0x00));  // 子命令
    command.append(static_cast<char>(region)); // 区域标识(0x01:BOOT0, 0x02:BOOT1)
    command.append(static_cast<char>(0xFF));  // PSN占位
    command.append(static_cast<char>(0xFF));  // PSN占位
    command.append(static_cast<char>(0xFF));  // PSN占位
    command.append(static_cast<char>(0xFF));  // PSN占位

    if (!SendCommand(command)) {
        addListMessage(QString("读取PSN命令发送失败! 区域: 0x%1").arg(region, 2, 16, QLatin1Char('0')).toUpper());
        return false;
    }

    addListMessage(QString("读取PSN命令已发送，区域: 0x%1").arg(region, 2, 16, QLatin1Char('0')).toUpper());

    QElapsedTimer timer;
    timer.start();
    const int timeout = 5000;

    while (timer.elapsed() < timeout) {
        if (!m_receiveBuffer.isEmpty()) {
            QByteArray response = m_receiveBuffer;
            m_receiveBuffer.clear();

            if (response.size() >= 7) {  // 命令(1) + 子命令(1) + 区域(1) + PSN(4)
                uint8_t cmd = static_cast<uint8_t>(response[0]);
                uint8_t subcmd = static_cast<uint8_t>(response[1]);
                uint8_t resp_region = static_cast<uint8_t>(response[2]);
                
                if (cmd == 0x04 && subcmd == 0x00 && resp_region == region) {
                    // 提取PSN值
                    QString psn = QString("%1 %2 %3 %4")
                        .arg(static_cast<uint8_t>(response[3]), 2, 16, QLatin1Char('0'))
                        .arg(static_cast<uint8_t>(response[4]), 2, 16, QLatin1Char('0'))
                        .arg(static_cast<uint8_t>(response[5]), 2, 16, QLatin1Char('0'))
                        .arg(static_cast<uint8_t>(response[6]), 2, 16, QLatin1Char('0')).toUpper();

                    QString regionName = (region == 0x01) ? "BOOT0" : "BOOT1";
                    addListMessage(QString("%1区域 PSN号: %2").arg(regionName).arg(psn));
                    return true;
                } else {
                    addListMessage(QString("收到无效响应: cmd=0x%1, subcmd=0x%2, region=0x%3")
                        .arg(cmd, 2, 16, QLatin1Char('0'))
                        .arg(subcmd, 2, 16, QLatin1Char('0'))
                        .arg(resp_region, 2, 16, QLatin1Char('0')).toUpper());
                }
            }
        }
        QCoreApplication::processEvents();
        QThread::msleep(10);
    }

    addListMessage(QString("读取PSN超时，区域: 0x%1").arg(region, 2, 16, QLatin1Char('0')).toUpper());
    return false;
}
