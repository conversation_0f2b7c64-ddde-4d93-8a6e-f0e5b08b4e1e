#include "candownload.h"
#include "ui_candownload.h"
#include <QFileDialog>
#include <QDebug>
#include <QRegExpValidator>
#include "ControlCAN.h"
#include <QtConcurrent>
#include <QThread>
#include <QValidator>
#include <QTimer>
#include <QEventLoop>
#include <QElapsedTimer>
#include <QScrollBar>
#include <QKeyEvent>
#include <QStatusBar>  
#include <QPushButton>  
#include <QMenu>

CANDownload::CANDownload(QWidget *parent)
    : QMainWindow(parent), ui(new Ui::CANDownload), m_autoScroll(true)
{
    ui->setupUi(this);
    
    // 设置应用程序图标
    setWindowIcon(QIcon(":/images/icon.png"));  // 替换为您的图标文件名
    
    // 调用初始化设置函数
    initializeSettings();
    addListMessage("当前上位机版本：V1.11");

    // 禁用所有相关的按钮
    ui->Download->setEnabled(false);
    ui->ClearFlag->setEnabled(false);
    ui->Connect->setEnabled(true);

    m_connect = 0;           // 0,未连接
    m_cannum = 0;            // 通道号
    m_devtype = VCI_USBCAN2; // 设备号
    m_devind = 0;            // 索引号
    BaudRateCal(ui->BaudRate->currentText());
    CodeAreaSel(ui->CodeArea->currentText());
    on_ComID_editingFinished();

    m_timer = new QTimer(this);
    m_timer->setInterval(500);
    // 连接定时器的超时信号到 lambda 函数
    connect(m_timer, &QTimer::timeout, this, [this]()
    {
        this->FeedDog();
    });

    // 连接滚动条信号到槽函数
    connect(ui->listWidget->verticalScrollBar(), &QScrollBar::valueChanged, this, &CANDownload::onListWidgetScrolled);

    // 设置列表小部件的上下文菜单策略
    ui->listWidget->setContextMenuPolicy(Qt::CustomContextMenu);
    connect(ui->listWidget, &QWidget::customContextMenuRequested, this, &CANDownload::showListContextMenu);
}

CANDownload::~CANDownload()
{
    CloseCAN();
    qDebug() << "关闭连接，程序退出";

    // 设置配置文件路径为应用程序运行目录下的config文件夹
    QString configDirPath = QCoreApplication::applicationDirPath() + "/config";
    QString settingsFile = configDirPath + "/settings.ini";
    QSettings settings(settingsFile, QSettings::IniFormat);

    // 保存当前目录到配置文件
    settings.setValue("lastDirectory", m_lastDirectory);

    delete ui;
}

void CANDownload::showListContextMenu(const QPoint &pos)
{
    QMenu contextMenu(tr("列表菜单"), this);

    QAction clearAction("清理所有消息", this);
    connect(&clearAction, &QAction::triggered, this, &CANDownload::clearAllMessages);
    clearAction.setEnabled(ui->listWidget->count() > 0);  // 使用 count() 而不是 isEmpty()
    contextMenu.addAction(&clearAction);

    // 显示菜单
    contextMenu.exec(ui->listWidget->mapToGlobal(pos));
}

void CANDownload::clearAllMessages()
{
    ui->listWidget->clear();
    m_pendingMessages.clear(); // 清理待处理的消息
    addListMessage("所有消息已清理");
    // 重置滚动状态
    m_autoScroll = true;
}

void CANDownload::initializeSettings()
{
    // 设置配置文件路径为应用程序运行目录下的config文件夹
    QString configDirPath = QCoreApplication::applicationDirPath() + "/config";
    QDir configDir(configDirPath);

    // 如果config文件夹不存在，则创建
    if (!configDir.exists())
    {
        configDir.mkpath(".");
    }

    // 指定配置文件路径
    QString settingsFile = configDirPath + "/settings.ini";
    QSettings settings(settingsFile, QSettings::IniFormat);

    // 加载保存的目录
    m_lastDirectory = settings.value("lastDirectory", QDir::homePath()).toString();

    // 初始化日志文件
    initializeLogFiles();
}


void CANDownload::initializeLogFiles()
{
    // 获取应用程序运行目录
    QString appDirPath = QCoreApplication::applicationDirPath();

    // 创建log文件夹路径
    QString logDirPath = appDirPath + "/log";
    QDir logDir(logDirPath);

    // 如果log文件夹不存在，则创建
    if (!logDir.exists())
    {
        logDir.mkpath(".");
    }

    // 创建日志文件名和路径
    QString timestamp = QDateTime::currentDateTime().toString("yyyyMMdd_hhmmss");
    m_logFilePath = logDirPath + "/" + timestamp + "_log.txt";
}



void CANDownload::TaskA()
{
   // 发送数据并等待响应
    if (!WriteFlag())
    {
        return;
    }

    QMetaObject::invokeMethod(m_timer, "start", Qt::QueuedConnection);//跨线程启动定时器

    if(!ReadVersion(0x1))
    {
        return;
    }
    if(!ReadVersion(0x2))
    {
        return;
    }
    if(!ReadVersion(0x3))
    {
        return;
    }

}

void CANDownload::TaskB()
{
    // 发送选择的bin文件
    if (!SendDataOverCAN())
    {
        return;
    }
    //写版本
    if (!WriteVersion())
    {
        return;
    }
    //测试运行
    if (!TestRun())
    {
        return;
    }

}


void CANDownload::FeedDog()
{
    VCI_CAN_OBJ frameinfo;

    memset(&frameinfo, 0, sizeof(frameinfo));

    m_devtype = VCI_USBCAN2; // 设备
    m_devind = 0;            // 设备索引
    m_cannum = 0;            // 第几路CAN
    frameinfo.ID = m_comTxID + 2;
    frameinfo.DataLen = 2;
    frameinfo.SendType = 2;
    frameinfo.RemoteFlag = 0; // 是否是远程帧
    frameinfo.ExternFlag = 0; // 是否是扩展帧
    frameinfo.Data[0] = 0x2;
    frameinfo.Data[1] = 0x0;

    if (VCI_Transmit(m_devtype, m_devind, m_cannum, &frameinfo, 1) == 1)
    {
        addListMessage("喂狗命令发送成功...");
    }
    else
    {
        addListMessage("喂狗命令发送失败!");
        CloseCAN(); // 关闭设备
        return ;
    }
}


bool CANDownload::ReadVersion(uint8_t region)
{
    VCI_CAN_OBJ frameinfo;

    memset(&frameinfo, 0, sizeof(frameinfo));

    m_devtype = VCI_USBCAN2; // 设备类型
    m_devind = 0;            // 设备索引
    m_cannum = 0;            // CAN 通道号
    frameinfo.ID = m_comTxID; // 发送的ID
    frameinfo.DataLen = 3;
    frameinfo.SendType = 2; // 标准发送
    frameinfo.RemoteFlag = 0; // 数据帧
    frameinfo.ExternFlag = 0; // 标准帧

    // 设置读取版本号命令，Data[1]根据区域不同设置为0x01, 0x02或0x03
    frameinfo.Data[0] = 0x01;
    frameinfo.Data[1] = 0x00; 
    frameinfo.Data[2] = region; // 区域标识 (0x01 = BOOT0, 0x02 = BOOT1, 0x03 = APP)

    if (VCI_Transmit(m_devtype, m_devind, m_cannum, &frameinfo, 1) != 1)
    {
        addListMessage("读取版本号命令发送失败!");
        CloseCAN(); // 关闭设备
        return false;
    }

    addListMessage(QString("读取版本号命令已发送，区域: %1").arg(region, 2, 16, QLatin1Char('0')).toUpper());

    VCI_CAN_OBJ rcvframeinfo[200];
    memset(rcvframeinfo, 0, sizeof(rcvframeinfo)); // 初始化接收缓冲区
    VCI_ERR_INFO errinfo;
    const int timeout = 5000;  // 设置超时时间为5000毫秒（5秒）
    QElapsedTimer timer;       // 使用QElapsedTimer来跟踪时间
    timer.start();             // 开始计时

    while (timer.elapsed() < timeout)
    {
        int len = VCI_Receive(m_devtype, m_devind, m_cannum, rcvframeinfo, 200, 100);

        if (len > 0)
        {
            for (int i = 0; i < len; ++i)
            {
                if (rcvframeinfo[i].ID == m_comRxID && rcvframeinfo[i].DataLen == 8 &&
                    rcvframeinfo[i].Data[0] == 0x01 && rcvframeinfo[i].Data[1] == 0x00)
                {

                    QString version = QString("区域: %1, 日期: %2-%3-%4-%5, 版本号: %6")
                        .arg(rcvframeinfo[i].Data[2], 2, 16, QLatin1Char('0'))  // 区域 0x03
                        .arg(rcvframeinfo[i].Data[3], 2, 16, QLatin1Char('0'))  // 年 20
                        .arg(rcvframeinfo[i].Data[4], 2, 16, QLatin1Char('0'))  // 年 24
                        .arg(rcvframeinfo[i].Data[5], 2, 16, QLatin1Char('0'))  // 月 12
                        .arg(rcvframeinfo[i].Data[6], 2, 16, QLatin1Char('0'))  // 日 30
                        .arg(rcvframeinfo[i].Data[7], 2, 16, QLatin1Char('0')); // 版本号 FF

                    addListMessage(QString("版本信息接收成功: %1").arg(version));
                    return true;
                }
            }
        }
        else
        {
            // 如果没有读到数据则必须调用此函数来读取出当前的错误码
            VCI_ReadErrInfo(m_devtype, m_devind, m_cannum, &errinfo);
        }
    }

    addListMessage("版本信息接收超时...");
    CloseCAN(); // 关闭设备
    return false; // 超时后退出函数
}

bool CANDownload::WriteVersion()
{
    // 检查文件名成员变量是否已保存
    if (m_fileName.isEmpty())
    {
        addListMessage("文件名未保存，无法执行版本号写入！");
        CloseCAN(); // 关闭设备
        return false;
    }

    // 提取文件名中的信息
    QFileInfo fileInfo(m_fileName);
    QString baseName = fileInfo.baseName(); // 获取不带扩展名的文件名，例如 "20240808FF"

    if (baseName.length() != 10)
    {
        addListMessage("文件名格式不正确，无法提取版本信息！");
        CloseCAN(); // 关闭设备
        return false;
    }

    QString yearHighStr = baseName.mid(0, 2); // 提取年份高位 "20"
    QString yearLowStr = baseName.mid(2, 2);  // 提取年份低位 "24"
    QString monthStr = baseName.mid(4, 2);    // 提取月份 "08"
    QString dayStr = baseName.mid(6, 2);      // 提取日期 "08"
    QString versionStr = baseName.mid(8, 2);  // 提取版本号 "FF"

    uint8_t yearHigh = static_cast<uint8_t>(yearHighStr.toInt());  // 年份的高字节 (20)
    uint8_t yearLow = static_cast<uint8_t>(yearLowStr.toInt());   // 年份的低字节 (24)
    uint8_t month = static_cast<uint8_t>(monthStr.toInt());
    uint8_t day = static_cast<uint8_t>(dayStr.toInt());
    uint8_t version = static_cast<uint8_t>(versionStr.toInt(nullptr, 16)); // 版本号为16进制

    VCI_CAN_OBJ frameinfo;
    memset(&frameinfo, 0, sizeof(frameinfo));

    m_devtype = VCI_USBCAN2; // 设备类型
    m_devind = 0;            // 设备索引
    m_cannum = 0;            // CAN 通道号

    frameinfo.ID = m_comTxID;
    frameinfo.DataLen = 8;   // 数据长度为 8 字节
    frameinfo.SendType = 2;
    frameinfo.RemoteFlag = 0; // 数据帧
    frameinfo.ExternFlag = 0; // 标准帧

    // 填充数据区域：小版本 + 日期 + 月份 + 年份(低) + 年份(高) + 区域ID + 其他信息
    frameinfo.Data[7] = version;        // 小版本号
    frameinfo.Data[6] = day;            // 日期
    frameinfo.Data[5] = month;          // 月份
    frameinfo.Data[4] = yearLow;        // 年份的低字节
    frameinfo.Data[3] = yearHigh;       // 年份的高字节
    frameinfo.Data[2] = m_CodeArea;     // 区域ID
    frameinfo.Data[1] = 0x03;           // 固定的数值或其他信息
    frameinfo.Data[0] = 0x01;           // 固定的数值或其他信息

    // 发送版本号写入命令
    if (VCI_Transmit(m_devtype, m_devind, m_cannum, &frameinfo, 1) != 1)
    {
        addListMessage("版本号写入命令发送失败！");
        CloseCAN(); // 关闭设备
        return false;
    }
    addListMessage("版本号写入命令发送成功，等待从EEPROM读取并验证版本号...");
    VCI_ClearBuffer(m_devtype, m_devind, m_cannum); // 清接收缓冲区

    // 等待从EEPROM读取并验证版本号
    VCI_CAN_OBJ rcvframeinfo[200];
    memset(rcvframeinfo, 0, sizeof(rcvframeinfo));  // 将整个数组初始化为0
    VCI_ERR_INFO errinfo;
    const int timeout = 5000;  // 设置超时时间为5000毫秒（5秒）
    QElapsedTimer timer;       // 使用QElapsedTimer来跟踪时间
    timer.start();             // 开始计时

    while (timer.elapsed() < timeout)
    {
        int len = VCI_Receive(m_devtype, m_devind, m_cannum, rcvframeinfo, 200, 100);

        if (len > 0)
        {
            for (int i = 0; i < len; ++i)
            {
                if (rcvframeinfo[i].ID == m_comRxID &&
                    rcvframeinfo[i].DataLen == 8 && // 确保数据长度为 8
                    rcvframeinfo[i].Data[7] == version &&  // 校验小版本号
                    rcvframeinfo[i].Data[6] == day &&      // 校验日期
                    rcvframeinfo[i].Data[5] == month &&    // 校验月份
                    rcvframeinfo[i].Data[4] == yearLow &&  // 校验年份低字节
                    rcvframeinfo[i].Data[3] == yearHigh && // 校验年份高字节
                    rcvframeinfo[i].Data[2] == m_CodeArea) // 校验区域ID
                {
                    addListMessage("版本号已成功写入并从EEPROM读取验证成功！");
                    return true;
                }
            }
        }
        else
        {
            VCI_ReadErrInfo(m_devtype, m_devind, m_cannum, &errinfo);
        }
    }

    addListMessage("版本号写入验证超时...");
    CloseCAN(); // 关闭设备
    return false; // 超时后退出函数
}


int CANDownload::WriteFlag()
{
    VCI_CAN_OBJ frameinfo;

    memset(&frameinfo, 0, sizeof(frameinfo));

    m_devtype = VCI_USBCAN2; // 设备
    m_devind = 0;            // 设备索引
    m_cannum = 0;            // 第几路CAN
    frameinfo.ID = m_comTxID;
    frameinfo.DataLen = 2;
    frameinfo.SendType = 2;
    frameinfo.RemoteFlag = 0; // 是否是远程帧
    frameinfo.ExternFlag = 0; // 是否是扩展帧
    frameinfo.Data[1] = 0x1;
    frameinfo.Data[0] = 0x0;

    if (VCI_Transmit(m_devtype, m_devind, m_cannum, &frameinfo, 1) == 1)
    {
        addListMessage("标志位置1命令发送成功,等待重启进入BOOT程序!");
        VCI_ClearBuffer(m_devtype, m_devind, m_cannum); // 清接收缓冲区
    }
    else
    {
        addListMessage("标志位置1命令发送失败!");
        CloseCAN(); // 关闭设备
        return false;
    }

    VCI_CAN_OBJ rcvframeinfo[200];
    memset(rcvframeinfo, 0, sizeof(rcvframeinfo));  // 将整个数组初始化为0
    VCI_ERR_INFO errinfo;
    const int timeout = 5000;  // 设置超时时间为5000毫秒（5秒）
    QElapsedTimer timer;       // 使用QElapsedTimer来跟踪时间
    timer.start();             // 开始计时

    while (timer.elapsed() < timeout)
    {
        int len = VCI_Receive(m_devtype, m_devind, m_cannum, rcvframeinfo, 200, 100);

        if (len > 0)
        {
            for (int i = 0; i < len; ++i)
            {
                if (rcvframeinfo[i].ID == m_comRxID &&
                    rcvframeinfo[i].Data[0] == 0x00 && rcvframeinfo[i].Data[1] == 0x01)
                {
                    addListMessage("ECU已经重启并进入BOOT!");
                    return true;
                }
            }
        }
        else
        {
            // 注意：如果没有读到数据则必须调用此函数来读取出当前的错误码，千万不能省略这一步（即使你可能不想知道错误码是什么）
            VCI_ReadErrInfo(m_devtype, m_devind, m_cannum, &errinfo);
        }
    }

    addListMessage("标志位置1命令响应接收超时...");
    CloseCAN(); // 关闭设备
    return false; // 超时后退出函数
}

bool CANDownload::TestRun()
{
    VCI_CAN_OBJ frameinfo;

    memset(&frameinfo, 0, sizeof(frameinfo));

    m_devtype = VCI_USBCAN2; // 设备
    m_devind = 0;            // 设备索引
    m_cannum = 0;            // 第几路CAN
    frameinfo.ID = m_comTxID;
    frameinfo.DataLen = 2;
    frameinfo.SendType = 2;
    frameinfo.RemoteFlag = 0; // 是否是远程帧
    frameinfo.ExternFlag = 0; // 是否是扩展帧
    frameinfo.Data[1] = 0x2;
    frameinfo.Data[0] = 0x1;

    if (VCI_Transmit(m_devtype, m_devind, m_cannum, &frameinfo, 1) == 1)
    {
        addListMessage("测试运行命令发送成功!");
        VCI_ClearBuffer(m_devtype, m_devind, m_cannum); // 清接收缓冲区
    }
    else
    {
        addListMessage("测试运行命令发送失败!");
        CloseCAN(); // 关闭设备
        return false;
    }

    VCI_CAN_OBJ rcvframeinfo[200];
    memset(rcvframeinfo, 0, sizeof(rcvframeinfo));  // 将整个数组初始化为0
    VCI_ERR_INFO errinfo;
    const int timeout = 5000;  // 设置超时时间为5000毫秒（5秒）
    QElapsedTimer timer;       // 使用QElapsedTimer来跟踪时间
    timer.start();             // 开始计时

    while (timer.elapsed() < timeout)
    {
        int len = VCI_Receive(m_devtype, m_devind, m_cannum, rcvframeinfo, 200, 100);

        if (len > 0)
        {
            for (int i = 0; i < len; ++i)
            {
                if (rcvframeinfo[i].ID == m_comRxID &&
                    rcvframeinfo[i].Data[1] == 0x02 && rcvframeinfo[i].Data[0] == 0x01)
                {
                    addListMessage("ECU已经 跳转（APP） / 写PSN并重启（BOOT）!");
                    return true;
                }
            }
        }
        else
        {
            // 注意：如果没有读到数据则必须调用此函数来读取出当前的错误码，千万不能省略这一步（即使你可能不想知道错误码是什么）
            VCI_ReadErrInfo(m_devtype, m_devind, m_cannum, &errinfo);
        }
    }

    addListMessage("测试运行命令响应接收超时...");
    CloseCAN(); // 关闭设备
    return false; // 超时后退出函数
}



void CANDownload::StartCAN()
{
    m_connect = 1; // 设置为连接状态

    VCI_INIT_CONFIG init_config;
    int index = 0;
    int cannum = 0; // 初始化 CAN 通道号，0、1

    init_config.AccCode = 0x0;
    init_config.AccMask = 0xffffffff; // 0xffffffff，接收所有数据
    init_config.Filter = 1;           // 1，单滤波
    init_config.Mode = 0;             // 0，正常模式
    init_config.Timing0 = m_Timing0;
    init_config.Timing1 = m_Timing1;

    if (VCI_OpenDevice(m_devtype, index, 0) != STATUS_OK)
    {
        addListMessage("打开设备失败!");
        CloseCAN();
        return;
    }
    else
    {
        addListMessage("打开设备成功...");
    }

    if (VCI_InitCAN(m_devtype, index, cannum, &init_config) != STATUS_OK)
    {
        addListMessage("初始化CAN失败!");
        CloseCAN();
        return;
    }
    else
    {
        addListMessage("初始化CAN成功...");
    }

    if (VCI_StartCAN(m_devtype, m_devind, m_cannum) != STATUS_OK)
    {
        addListMessage("USBCAN启动失败...");
        CloseCAN();
        return;
    }
    else
    {
        addListMessage("USBCAN启动成功...");
    }

    // 使用 QMetaObject::invokeMethod 处理 UI 操作，确保在主线程中执行
    QMetaObject::invokeMethod(this, [this]()
    {
        ui->Connect->setText(tr("断开"));
        ui->Download->setEnabled(true);
        ui->ClearFlag->setEnabled(true);
        ui->Connect->setEnabled(true);
    }, Qt::QueuedConnection);

    QCoreApplication::processEvents(); // 强制处理事件，立即更新界面
}

void CANDownload::CloseCAN()
{
    // 确保在主线程中停止定时器
    QMetaObject::invokeMethod(m_timer, "stop", Qt::QueuedConnection);

    // 关闭 CAN 设备的操作，这些操作不涉及 UI，可以直接在当前线程中执行
    VCI_ResetCAN(m_devtype, m_devind, 0);
    VCI_ResetCAN(m_devtype, m_devind, 1);
    VCI_CloseDevice(m_devtype, m_devind);
    m_connect = 0;
    QThread::msleep(1000); // 增加延时以等待设备关闭

    // 日志输出可以在当前线程中进行
    qDebug() << "连接已关闭.";

    // 使用 invokeMethod 确保 UI 更新操作在主线程中进行
    QMetaObject::invokeMethod(this, [this]() {
        // 更新 UI 状态
        ui->Connect->setText(tr("连接"));
        ui->Download->setEnabled(false);
        ui->ClearFlag->setEnabled(false);
        ui->Connect->setEnabled(true);

        // 处理挂起的事件，确保 UI 的及时更新
        QCoreApplication::processEvents();
    }, Qt::QueuedConnection);
}


void CANDownload::BaudRateCal(const QString &arg1)
{
    // 根据选择的波特率更新m_Timing0和m_Timing1
    if (arg1 == "100K")
    {
        m_Timing0 = 0x04;
        m_Timing1 = 0x1C;
    }
    else if (arg1 == "125K")
    {
        m_Timing0 = 0x03;
        m_Timing1 = 0x1C;
    }
    else if (arg1 == "250K")
    {
        m_Timing0 = 0x01;
        m_Timing1 = 0x1C;
    }
    else if (arg1 == "500K")
    {
        m_Timing0 = 0x00;
        m_Timing1 = 0x1C;
    }
    else if (arg1 == "1M")
    {
        m_Timing0 = 0x00;
        m_Timing1 = 0x14;
    }
    else
    {
        qDebug() << "未知波特率设置";
        return;
    }
    QString message = QString("当前波特率为: %1").arg(arg1);
    addListMessage(message);
}

void CANDownload::CodeAreaSel(const QString &arg1)
{
    // 根据选择的编程区域更新m_CodeArea
    if (arg1 == "BOOT0")
    {
        m_CodeArea = 1;
    }
    else if (arg1 == "BOOT1")
    {
        m_CodeArea = 2;
    }
    else if (arg1 == "APP")
    {
        m_CodeArea = 3;
    }
    else
    {
        qDebug() << "未知编程区域";
        return;
    }
    QString message = QString("当前编程区域为: %1").arg(arg1);
    addListMessage(message);
}

void CANDownload::on_BaudRate_currentTextChanged(const QString &arg1)
{
    qDebug() << "BaudRate selected:" << arg1;
    BaudRateCal(arg1);
}

void CANDownload::on_CodeArea_currentTextChanged(const QString &arg1)
{
    qDebug() << "CodeArea:" << arg1;
    CodeAreaSel(arg1);
}

void CANDownload::on_ComID_editingFinished()
{
    QString text = ui->ComID->text().trimmed().toUpper(); // 格式化输入为大写并去除首尾空格
    ui->ComID->setText(text);

    // 设置16进制数的正则表达式
    QRegExp hexRegExp("^([0-7][0-9A-F]{0,2}|[0-9A-F]{1,2})$");
    QRegExpValidator validator(hexRegExp, this);

    int pos = 0;
    if (validator.validate(text, pos) != QValidator::Acceptable)
    {
        // 如果输入不合法
        qDebug() << "Invalid input. Resetting to default value '1'.";
        addListMessage("无效输入，取值范围0-7FF");
        ui->ComID->setText("1");
    }
    else
    {
        // 输入合法，检查数值范围
        bool ok;
        int value = text.toInt(&ok, 16); // 转换为整数
        if (!ok || value < 0 || value > 0x7FF)
        {
            addListMessage("无效输入，取值范围0-7FF");
            ui->ComID->setText("1");
        }
        else
        {
            text = "当前通信ID为：" + text;
            addListMessage(text);
            m_comTxID = value;
            m_comRxID = m_comTxID + 1;
        }
    }
}

void CANDownload::addListMessage(const QString &message)
{
    if (QThread::currentThread() == qApp->thread()) {
        QString timestamp = QDateTime::currentDateTime().toString("[yyyy-MM-dd hh:mm:ss] ");
        QString logEntry = timestamp + message;

        QScrollBar *scrollBar = ui->listWidget->verticalScrollBar();
        bool wasAtBottom = scrollBar->value() == scrollBar->maximum();

        if (wasAtBottom) {
            checkAndCleanListWidget();
            ui->listWidget->addItem(logEntry);
            QTimer::singleShot(0, scrollBar, [scrollBar]() {
                scrollBar->setValue(scrollBar->maximum());
                qDebug() << "Scrolled to bottom after adding message";
            });
        } else {
            // 如果滚动条不在底部，只保存消息，不更新列表
            m_pendingMessages.append(logEntry);
            qDebug() << "Message queued, not updating list view";
        }

        // 将日志条目保存到文件
        QFile logFile(m_logFilePath);
        if (logFile.open(QIODevice::Append | QIODevice::Text)) {
            QTextStream out(&logFile);
            out << logEntry << "\n";
            logFile.close();
        }
    } else {
        QMetaObject::invokeMethod(this, "addListMessage", Qt::QueuedConnection, Q_ARG(QString, message));
    }
}

void CANDownload::checkAndCleanListWidget()
{
    const int maxItems = 500; // 设置最大列表项数量
    int currentCount = ui->listWidget->count();
    if (currentCount > maxItems) {
        int itemsToRemove = currentCount - maxItems;
        
        QScrollBar *scrollBar = ui->listWidget->verticalScrollBar();
        bool wasAtBottom = scrollBar->value() == scrollBar->maximum();
        
        ui->listWidget->setUpdatesEnabled(false);
        
        for (int i = 0; i < itemsToRemove; ++i) {
            delete ui->listWidget->takeItem(0);
        }
        
        ui->listWidget->setUpdatesEnabled(true);
        
        if (wasAtBottom || m_autoScroll) {
            QTimer::singleShot(0, this, [this]() {
                ui->listWidget->scrollToBottom();
                qDebug() << "Scrolled to bottom after cleaning";
            });
        } else {
            int newScrollValue = qMin(scrollBar->value(), scrollBar->maximum());
            scrollBar->setValue(newScrollValue);
            qDebug() << "Restored scroll position after cleaning:" << newScrollValue;
        }
        
        qDebug() << "列表清理完成,当前项目数:" << ui->listWidget->count();
    }
}


void CANDownload::onListWidgetScrolled()
{
    QScrollBar *scrollBar = ui->listWidget->verticalScrollBar();
    bool isAtBottom = (scrollBar->value() >= scrollBar->maximum() - 5); // 允许一些误差

    if (isAtBottom && !m_pendingMessages.isEmpty()) {
        // 更新列表视图
        ui->listWidget->addItems(m_pendingMessages);
        m_pendingMessages.clear();
        
        // 确保滚动到底部
        QTimer::singleShot(0, scrollBar, [scrollBar]() {
            scrollBar->setValue(scrollBar->maximum());
        });
        
        qDebug() << "Updated list view with pending messages";
    }

    if (isAtBottom != m_autoScroll) {
        m_autoScroll = isAtBottom;
        qDebug() << "Auto-scroll state changed. Now:" << m_autoScroll;
    }
}

void CANDownload::keyPressEvent(QKeyEvent *event)
{
    if (event->key() == Qt::Key_Return || event->key() == Qt::Key_Enter)
    {
        // 按下回车键时恢复自动滚动
        m_autoScroll = true;
        ui->listWidget->scrollToBottom();
        qDebug() << "Enter key pressed, restoring auto-scroll";
    }
    else
    {
        QMainWindow::keyPressEvent(event);
    }
}




void CANDownload::OpenFile()
{
    QString filter = "Binary files (*.bin)"; // 定义文件过滤器

    // 使用上一次保存的目录
    QString fileName = QFileDialog::getOpenFileName(this, tr("打开文件"), m_lastDirectory, filter);

    if (!fileName.isEmpty())
    {
        // 保存当前目录
        QFileInfo fileInfo(fileName);
        m_lastDirectory = fileInfo.absolutePath();

        // 输出文件名
        addListMessage(QString("打开的文件：%1").arg(fileName));

        QString baseName = fileInfo.baseName(); // 获取文件名，不含扩展名

        QRegExp regExp("^\\d{4}\\d{2}\\d{2}[0-9A-F]{2}$"); // 例如: 20241230FF
        if (regExp.exactMatch(baseName))
        {
            addListMessage(QString("文件名格式正确: %1").arg(baseName));
            m_fileName = fileName; // 将完整的文件名（包括路径）保存到成员变量中

            QFile file(fileName);
            if (file.open(QIODevice::ReadOnly))
            {
                m_fileContent = file.readAll(); // 读取整个文件内容
                file.close();
                addListMessage(QString("文件内容读取成功，大小: %1 字节").arg(m_fileContent.size()));
            }
            else
            {
                addListMessage("文件读取失败");
                m_fileContent.clear(); // 读取失败时清空内容
            }
        }
        else
        {
            addListMessage(QString("文件名格式错误: %1，文件名应为年+月+日+小版本号，例如：20241230FF.bin").arg(baseName));
            m_fileName.clear(); // 如果格式不正确，清空成员变量
            m_fileContent.clear(); // 清空文件内容
        }
    }
}


uint16_t CANDownload::CalculateCRC(const QByteArray &data)
{
    static const uint16_t crc16tab[256] = {
        0x0000, 0x1021, 0x2042, 0x3063, 0x4084, 0x50a5, 0x60c6, 0x70e7,
        0x8108, 0x9129, 0xa14a, 0xb16b, 0xc18c, 0xd1ad, 0xe1ce, 0xf1ef,
        0x1231, 0x0210, 0x3273, 0x2252, 0x52b5, 0x4294, 0x72f7, 0x62d6,
        0x9339, 0x8318, 0xb37b, 0xa35a, 0xd3bd, 0xc39c, 0xf3ff, 0xe3de,
        0x2462, 0x3443, 0x0420, 0x1401, 0x64e6, 0x74c7, 0x44a4, 0x5485,
        0xa56a, 0xb54b, 0x8528, 0x9509, 0xe5ee, 0xf5cf, 0xc5ac, 0xd58d,
        0x3653, 0x2672, 0x1611, 0x0630, 0x76d7, 0x66f6, 0x5695, 0x46b4,
        0xb75b, 0xa77a, 0x9719, 0x8738, 0xf7df, 0xe7fe, 0xd79d, 0xc7bc,
        0x48c4, 0x58e5, 0x6886, 0x78a7, 0x0840, 0x1861, 0x2802, 0x3823,
        0xc9cc, 0xd9ed, 0xe98e, 0xf9af, 0x8948, 0x9969, 0xa90a, 0xb92b,
        0x5af5, 0x4ad4, 0x7ab7, 0x6a96, 0x1a71, 0x0a50, 0x3a33, 0x2a12,
        0xdbfd, 0xcbdc, 0xfbbf, 0xeb9e, 0x9b79, 0x8b58, 0xbb3b, 0xab1a,
        0x6ca6, 0x7c87, 0x4ce4, 0x5cc5, 0x2c22, 0x3c03, 0x0c60, 0x1c41,
        0xedae, 0xfd8f, 0xcdcc, 0xdded, 0xad2a, 0xbd0b, 0x8d68, 0x9d49,
        0x7e97, 0x6eb6, 0x5ed5, 0x4ef4, 0x3e13, 0x2e32, 0x1e51, 0x0e70,
        0xff9f, 0xefbe, 0xdfdd, 0xcffc, 0xbf1b, 0xaf3a, 0x9f59, 0x8f78,
        0x9188, 0x81a9, 0xb1ca, 0xa1eb, 0xd10c, 0xc12d, 0xf14e, 0xe16f,
        0x1080, 0x00a1, 0x30c2, 0x20e3, 0x5004, 0x4025, 0x7046, 0x6067,
        0x83b9, 0x9398, 0xa3fb, 0xb3da, 0xc33d, 0xd31c, 0xe37f, 0xf35e,
        0x02b1, 0x1290, 0x22f3, 0x32d2, 0x4235, 0x5214, 0x6277, 0x7256,
        0xb5ea, 0xa5cb, 0x95a8, 0x8589, 0xf56e, 0xe54f, 0xd52c, 0xc50d,
        0x34e2, 0x24c3, 0x14a0, 0x0481, 0x7466, 0x6447, 0x5424, 0x4405,
        0xa7db, 0xb7fa, 0x8799, 0x97b8, 0xe75f, 0xf77e, 0xc71d, 0xd73c,
        0x26d3, 0x36f2, 0x0691, 0x16b0, 0x6657, 0x7676, 0x4615, 0x5634,
        0xd94c, 0xc96d, 0xf90e, 0xe92f, 0x99c8, 0x89e9, 0xb98a, 0xa9ab,
        0x5844, 0x4865, 0x7806, 0x6827, 0x18c0, 0x08e1, 0x3882, 0x28a3,
        0xcb7d, 0xdb5c, 0xeb3f, 0xfb1e, 0x8bf9, 0x9bd8, 0xabbb, 0xbb9a,
        0x4a75, 0x5a54, 0x6a37, 0x7a16, 0x0af1, 0x1ad0, 0x2ab3, 0x3a92,
        0xfd2e, 0xed0f, 0xdd6c, 0xcd4d, 0xbdaa, 0xad8b, 0x9de8, 0x8dc9,
        0x7c26, 0x6c07, 0x5c64, 0x4c45, 0x3ca2, 0x2c83, 0x1ce0, 0x0cc1,
        0xef1f, 0xff3e, 0xcf5d, 0xdf7c, 0xaf9b, 0xbfba, 0x8fd9, 0x9ff8,
        0x6e17, 0x7e36, 0x4e55, 0x5e74, 0x2e93, 0x3eb2, 0x0ed1, 0x1ef0
    };
    uint16_t crc = 0;

    for (int i = 0; i < data.size(); ++i)
    {
        crc = (crc << 8) ^ crc16tab[((crc >> 8) ^ static_cast<uint8_t>(data[i])) & 0xFF];
    }

    return crc;
}


bool CANDownload::SendDataOverCAN()
{
    const int chunkSize = 1024; // 每块的大小为1024字节
    int offset = 0;
    uint16_t cntChunk = 0;
    int totalChunks = (m_fileContent.size() + chunkSize - 1) / chunkSize; // 计算总块数
    QElapsedTimer timer;  // 创建一个 QElapsedTimer 对象
    timer.start();  // 开始计时

    while (offset < m_fileContent.size())
    {
        int bytesToSend = qMin(chunkSize, m_fileContent.size() - offset);
        QByteArray chunk = m_fileContent.mid(offset, bytesToSend);

        cntChunk++;
        if(cntChunk >= totalChunks)cntChunk = totalChunks;
        double progress = (static_cast<double>(cntChunk) / totalChunks) * 100.0;
        addListMessage(QString("下载进度： %1%").arg(progress, 0, 'f', 1)); // 显示进度并保留两位小数

        bool blockSent = false;
        while (!blockSent)
        {
            // 1. 计算并发送包含CRC和区域信息的帧
            uint16_t crc = CalculateCRC(chunk); // 计算当前块的CRC
            uint8_t region = m_CodeArea;

            VCI_CAN_OBJ frameinfo;
            memset(&frameinfo, 0, sizeof(frameinfo));

            m_devtype = VCI_USBCAN2;
            m_devind = 0;
            m_cannum = 0;

            frameinfo.ID = m_comTxID;
            frameinfo.DataLen = 8;  // 发送区域和CRC共3个字节
            frameinfo.SendType = 2;
            frameinfo.RemoteFlag = 0;
            frameinfo.ExternFlag = 0;

            frameinfo.Data[7] = static_cast<uint8_t>(bytesToSend & 0xFF);        // 低字节
            frameinfo.Data[6] = static_cast<uint8_t>((bytesToSend >> 8) & 0xFF); // 中间字节
            frameinfo.Data[5] = static_cast<uint8_t>((bytesToSend >> 16) & 0xFF); // 高字节
            frameinfo.Data[4] = region;                                  // 区域信息
            frameinfo.Data[3] = static_cast<uint8_t>(crc & 0xFF);        // CRC低字节
            frameinfo.Data[2] = static_cast<uint8_t>((crc >> 8) & 0xFF); // CRC高字节
            frameinfo.Data[1] = 0x01;
            frameinfo.Data[0] = 0x01;

            if (VCI_Transmit(m_devtype, m_devind, m_cannum, &frameinfo, 1) != 1)
            {
                addListMessage("CRC和区域信息帧发送失败!");
                CloseCAN(); // 关闭设备
                return false;
            }

            // 2. 发送当前块的数据
            if (!SendChunkOverCAN(chunk))
            {
                addListMessage("发送数据块失败，终止发送!");
                CloseCAN(); // 关闭设备
                return false;
            }

            // 3. 发送完块后等待响应
            CANResponseStatus responseStatus = WaitForDownLoadResponse();

            if (responseStatus == ResponseSuccess)
            {
                blockSent = true; // 校验通过，继续发送下一块
            }
            else if (responseStatus == ResponseRetry)
            {
                addListMessage("重发当前块...");
                // 当重发时，blockSent 保持为 false，继续重发 CRC 帧和数据块
            }
            else if (responseStatus == ResponseTimeout)
            {
                addListMessage("等待响应超时，终止发送!");
                CloseCAN(); // 关闭设备
                return false; // 超时，终止发送
            }
        }

        offset += bytesToSend;
    }
    cntChunk = 0;
    addListMessage(QString("文件全部发送完毕, 大小： %1 字节").arg(m_fileContent.size()));
    // 计算并显示花费的时间
    addListMessage(QString("花费时间： %1 秒").arg(timer.elapsed() / 1000.0, 0, 'f', 3));
    return true;
}



bool CANDownload::SendChunkOverCAN(const QByteArray &chunk)
{
    const int frameSize = 8; // 每次发送8个字节
    int offset = 0;

    while (offset < chunk.size())
    {
        int bytesToSend = qMin(frameSize, chunk.size() - offset);

        VCI_CAN_OBJ frameinfo;
        memset(&frameinfo, 0, sizeof(frameinfo));

        m_devtype = VCI_USBCAN2;
        m_devind = 0;
        m_cannum = 0;

        frameinfo.ID = m_comTxID;
        frameinfo.DataLen = bytesToSend;
        frameinfo.SendType = 2;
        frameinfo.RemoteFlag = 0;
        frameinfo.ExternFlag = 0;

        memcpy(frameinfo.Data, chunk.constData() + offset, bytesToSend);

        if (VCI_Transmit(m_devtype, m_devind, m_cannum, &frameinfo, 1) != 1)
        {
            addListMessage(QString("发送数据帧失败，偏移量: %1").arg(offset));
            CloseCAN(); // 关闭设备
            return false;
        }

        offset += bytesToSend;
    }

    return true;
}

CANResponseStatus CANDownload::WaitForDownLoadResponse()
{
    VCI_CAN_OBJ rcvframeinfo[200];
    memset(rcvframeinfo, 0, sizeof(rcvframeinfo));

    VCI_ERR_INFO errinfo;
    const int timeout = 5000;  // 设置超时时间为5000毫秒（5秒）
    QElapsedTimer timer;
    timer.start();

    while (timer.elapsed() < timeout)
    {
        int len = VCI_Receive(m_devtype, m_devind, m_cannum, rcvframeinfo, 200, 100);

        if (len > 0)
        {
            for (int i = 0; i < len; ++i)
            {
                if (rcvframeinfo[i].ID == m_comRxID &&
                    rcvframeinfo[i].Data[0] == 0x01 && rcvframeinfo[i].Data[1] == 0x01)
                {
                    // 检查响应类型
                    uint8_t responseType = rcvframeinfo[i].Data[2];
                    if (responseType == 0x01)
                    {
                        addListMessage("校验通过，继续发送下一块...");
                        return ResponseSuccess; // 校验通过，继续发送下一块
                    }
                    else if (responseType == 0x02)
                    {
                        addListMessage("校验不通过，重发当前块...");
                        return ResponseRetry; // 校验不通过，重发当前块
                    }
                    else
                    {
                        addListMessage("校验码错误！");
                        return ResponseTimeout; // 校验码错误，按照超时处理
                    }
                }
            }
        }
        else
        {
            VCI_ReadErrInfo(m_devtype, m_devind, m_cannum, &errinfo);
        }
    }

    addListMessage("等待响应超时...");
    return ResponseTimeout; // 超时
}


bool CANDownload::ClearFlag()
{
    VCI_CAN_OBJ frameinfo;

    memset(&frameinfo, 0, sizeof(frameinfo));

    m_devtype = VCI_USBCAN2; // 设备
    m_devind = 0;            // 设备索引
    m_cannum = 0;            // 第几路CAN
    frameinfo.ID = m_comTxID;
    frameinfo.DataLen = 2;
    frameinfo.SendType = 2;
    frameinfo.RemoteFlag = 0; // 是否是远程帧
    frameinfo.ExternFlag = 0; // 是否是扩展帧
    frameinfo.Data[1] = 0x2;
    frameinfo.Data[0] = 0x0;

    if (VCI_Transmit(m_devtype, m_devind, m_cannum, &frameinfo, 1) == 1)
    {
        addListMessage("标志位清0命令发送成功!");
        VCI_ClearBuffer(m_devtype, m_devind, m_cannum); // 清接收缓冲区
    }
    else
    {
        addListMessage("标志位清0命令发送失败!");
        CloseCAN(); // 关闭设备
        return false;
    }

    VCI_CAN_OBJ rcvframeinfo[200];
    memset(rcvframeinfo, 0, sizeof(rcvframeinfo));  // 将整个数组初始化为0
    VCI_ERR_INFO errinfo;
    const int timeout = 8000;  // 设置超时时间为5000毫秒（5秒）
    QElapsedTimer timer;       // 使用QElapsedTimer来跟踪时间
    timer.start();             // 开始计时

    while (timer.elapsed() < timeout)
    {
        int len = VCI_Receive(m_devtype, m_devind, m_cannum, rcvframeinfo, 200, 100);

        if (len > 0)
        {
            for (int i = 0; i < len; ++i)
            {
                if (rcvframeinfo[i].ID == m_comRxID &&
                    rcvframeinfo[i].Data[1] == 0x02 && rcvframeinfo[i].Data[0] == 0x01)
                {
                    addListMessage("标志位清0成功，ECU已经进入APP!");
                    QMetaObject::invokeMethod(m_timer, "stop", Qt::QueuedConnection);//关闭喂狗定时器
                    return true ;
                }
            }
        }
        else
        {
            // 注意：如果没有读到数据则必须调用此函数来读取出当前的错误码，千万不能省略这一步（即使你可能不想知道错误码是什么）
            VCI_ReadErrInfo(m_devtype, m_devind, m_cannum, &errinfo);
        }
    }

    addListMessage("标志位清0命令响应接收超时...");
    CloseCAN(); // 关闭设备
    return false; // 超时后退出函数
}


void CANDownload::on_Connect_clicked()
{
    qDebug() << "连接按钮被点击";

    ui->Connect->setEnabled(false); // 禁用按钮，防止重复点击
    ui->Download->setEnabled(false);
    ui->ClearFlag->setEnabled(false);
    QCoreApplication::processEvents(); // 强制处理事件，立即更新界面

    if (m_connect == 1)
    {
        // 已经连接状态，断开连接
        addListMessage("已经处于连接状态，断开连接");
        CloseCAN(); // 关闭设备
        return;
    }
    addListMessage("先确保关闭设备，然后再启动设备");
    CloseCAN(); // 此处先确保关闭设备
    StartCAN(); // 启动CAN卡
    

    // 这是耗时任务，需要启动单独的线程接收，避免卡死
    QtConcurrent::run(this, &CANDownload::TaskA);
}

void CANDownload::on_Download_clicked()
{
    qDebug() << "下载按钮被点击";
    OpenFile();

    QtConcurrent::run(this, &CANDownload::TaskB);

}


void CANDownload::on_ClearFlag_clicked()
{
    QtConcurrent::run(this, &CANDownload::ClearFlag);
}


void CANDownload::testListCleaning()
{
    for (int i = 0; i < 1100; ++i) {
        addListMessage(QString("测试消息 %1").arg(i));
        QCoreApplication::processEvents();  // 处理待处理的事件，刷新UI
        QThread::msleep(1);  // 短暂延迟，使过程可见
    }
}
