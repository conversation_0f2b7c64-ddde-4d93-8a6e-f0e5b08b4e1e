#ifndef CANDOWNLOAD_H
#define CANDOWNLOAD_H

#include <QMainWindow>

enum CANResponseStatus {
    ResponseSuccess,   // 校验通过
    ResponseRetry,     // 校验不通过，需要重发
    ResponseTimeout    // 等待响应超时
};

QT_BEGIN_NAMESPACE
namespace Ui { class CANDownload; }
QT_END_NAMESPACE

class CANDownload : public QMainWindow
{
    Q_OBJECT

public:
    CANDownload(QWidget *parent = nullptr);
    ~CANDownload();

private slots:
    void on_Connect_clicked();       // 处理连接操作
    void on_Download_clicked();      // 处理下载操作
    void on_ClearFlag_clicked();     // 处理标志位清0操作

    void TaskA();
    void TaskB();
    void StartCAN();
    void CloseCAN();
    int WriteFlag();      // 处理写标志位操作
    bool TestRun();       // 处理测试运行操作
    bool ClearFlag();
    void OpenFile();      // 处理打开文件操作
    void FeedDog();
    bool ReadVersion(uint8_t region);

    void on_BaudRate_currentTextChanged(const QString &arg1);
    void on_CodeArea_currentTextChanged(const QString &arg1);
    void on_ComID_editingFinished();

    void addListMessage(const QString &message);
    void BaudRateCal(const QString &arg1);
    void CodeAreaSel(const QString &arg1);

    bool SendChunkOverCAN(const QByteArray &chunk);
    CANResponseStatus WaitForDownLoadResponse();
    bool SendDataOverCAN();
    uint16_t CalculateCRC(const QByteArray &data);
    bool WriteVersion();
    void initializeSettings(); // 用于初始化设置的函数

    void initializeLogFiles(); // 初始化日志文件的函数

    void showListContextMenu(const QPoint &pos);
    void clearAllMessages();

public slots:
    void testListCleaning();

private:
    Ui::CANDownload *ui;
    unsigned int m_connect;
    unsigned int m_cannum;
    unsigned int m_devtype;
    unsigned long m_devind;
    unsigned int m_Timing0;
    unsigned int m_Timing1;
    unsigned long m_comTxID;
    unsigned long m_comRxID;
    uint8_t m_CodeArea;
    QTimer *m_timer;
    QString m_fileName; // 用于保存文件名的成员变量
    QByteArray m_fileContent; // 保存文件内容
    QString m_lastDirectory;  // 用于保存上一次的文件目录

    QString m_logFilePath;     // 用于保存日志文件路径

    void checkAndCleanListWidget();
    void onListWidgetScrolled();
    void keyPressEvent(QKeyEvent *event) override;
    bool m_autoScroll;
    QStringList m_pendingMessages;
};

#endif // CANDOWNLOAD_H
