#include "mainwindow.h"
#include "ui_mainwindow.h"
#include "candownload.h"
#include "ethernetdownload.h"
#include <QDebug>
#include <QStatusBar>

MainWindow::MainWindow(QWidget *parent)
    : QMainWindow(parent)
    , ui(new Ui::MainWindow)
    , m_canDownload(nullptr)
    , m_ethernetDownload(nullptr)
    , m_isCANMode(true)  // 默认为 CAN 下载模式
{
    ui->setupUi(this);
    
    // 设置应用程序图标
    setWindowIcon(QIcon(":/images/icon.png"));
    
    // 设置初始按钮文本
    ui->switchButton->setText("切换到以太网下载");
    
    // 设置窗口标题，包含版本号
    setWindowTitle(QString("BootLoader %1").arg(APP_VERSION));
    
    // 在状态栏显示版本号
    statusBar()->showMessage(QString("版本: %1").arg(APP_VERSION));
}

MainWindow::~MainWindow()
{
    // 关闭窗口时回收所有资源
    closeCurrentWindow();
    delete ui;
}

void MainWindow::closeEvent(QCloseEvent *event)
{
    // 关闭所有子窗口
    closeCurrentWindow();
    // 接受关闭事件
    event->accept();
}

void MainWindow::closeCurrentWindow()
{
    // 关闭并释放 CAN 下载窗口
    if (m_canDownload)
    {
        m_canDownload->close();  // 先关闭窗口
        delete m_canDownload;    // 再释放资源
        m_canDownload = nullptr;
    }
    
    // 关闭并释放以太网下载窗口
    if (m_ethernetDownload)
    {
        m_ethernetDownload->close();  // 先关闭窗口
        delete m_ethernetDownload;    // 再释放资源
        m_ethernetDownload = nullptr;
    }
}

void MainWindow::on_switchButton_clicked()
{
    // 关闭当前打开的窗口
    closeCurrentWindow();

    // 切换模式
    m_isCANMode = !m_isCANMode;

    if (m_isCANMode)
    {
        // 切换到 CAN 下载模式
        m_canDownload = new CANDownload(this);  // 设置为主窗口的子窗口
        m_canDownload->setAttribute(Qt::WA_DeleteOnClose);  // 关闭时自动删除
        m_canDownload->setWindowFlags(m_canDownload->windowFlags() | Qt::Window);  // 设置为独立窗口
        connect(m_canDownload, &CANDownload::destroyed, [this]() {
            m_canDownload = nullptr;  // 窗口关闭时将指针置空
        });
        m_canDownload->show();
        ui->switchButton->setText("切换到以太网下载");
    }
    else
    {
        // 切换到以太网下载模式
        m_ethernetDownload = new EthernetDownload(this);  // 设置为主窗口的子窗口
        m_ethernetDownload->setAttribute(Qt::WA_DeleteOnClose);  // 关闭时自动删除
        m_ethernetDownload->setWindowFlags(m_ethernetDownload->windowFlags() | Qt::Window);  // 设置为独立窗口
        connect(m_ethernetDownload, &EthernetDownload::destroyed, [this]() {
            m_ethernetDownload = nullptr;  // 窗口关闭时将指针置空
        });
        m_ethernetDownload->show();
        ui->switchButton->setText("切换到CAN下载");
    }
} 