#ifndef ETHERNETDOWNLOAD_H
#define ETHERNETDOWNLOAD_H

#include <QtWidgets/QMainWindow>
#include <QtWidgets/QWidget>
#include <QtNetwork/QUdpSocket>
#include <QtNetwork/QNetworkInterface>
#include <QtCore/QTimer>
#include <QtCore/QElapsedTimer>
#include <QtCore/QByteArray>
#include <QtCore/QString>
#include <QtCore/QStringList>
#include "common_types.h"
#include "version.h"

QT_BEGIN_NAMESPACE
namespace Ui { class EthernetDownload; }
QT_END_NAMESPACE

class EthernetDownload : public QMainWindow
{
    Q_OBJECT

public:
    explicit EthernetDownload(QWidget *parent = nullptr);
    ~EthernetDownload();

private slots:
    void on_Connect_clicked();
    void on_Download_clicked();
    void on_ClearFlag_clicked();
    void on_CodeArea_currentTextChanged(const QString &arg1);
    void showListContextMenu(const QPoint &pos);
    void clearAllMessages();
    void onListWidgetScrolled();
    void handleSocketError(QAbstractSocket::SocketError socketError);
    void handleSocketReadyRead();
    void selectAllMessages();
    void copySelectedMessages();
    void addListMessage(const QString &message);
    void handleParaTargetIPModify();
    bool ModifyParaTargetIP(const QString &newIP);

protected:
    void keyPressEvent(QKeyEvent *event) override;

private:
    Ui::EthernetDownload *ui;
    QTimer *m_timer;
    bool m_autoScroll;
    QStringList m_pendingMessages;
    QString m_lastDirectory;
    QString m_logFilePath;
    QString m_fileName;
    QByteArray m_fileContent;
    QUdpSocket *m_socket;
    QByteArray m_receiveBuffer;

    // 网络通信相关变量
    QString m_localIP;
    quint16 m_localPort;
    QString m_targetIP;
    quint16 m_targetPort;
    int m_connect;
    int m_CodeArea;
    QString m_paraTargetIP;

    // 私有函数
    void initializeSettings();
    void initializeLogFiles();
    void TaskA();
    void TaskB();
    void StartNetwork();
    void CloseNetwork();
    void CodeAreaSel(const QString &arg1);
    void OpenFile();
    void FeedDog();
    bool ReadVersion(uint8_t area);
    bool WriteVersion();
    bool WriteFlag();
    bool TestRun();
    bool ClearFlag();
    bool SendDataOverNetwork();
    bool SendChunkOverNetwork(const QByteArray &chunk);
    Common::CANResponseStatus WaitForDownLoadResponse();
    void checkAndCleanListWidget();
    uint16_t CalculateCRC(const QByteArray &data);
    void testListCleaning();
    bool SendCommand(const QByteArray &command);
    bool WaitForResponse(const QByteArray &expectedResponse, int timeout = 5000);
    void initializeNetworkInterfaces();
    QMap<QString, QString> m_networkInterfaces; // 存储网卡名称和IP地址的映射
    bool ReadPSN(uint8_t region);
};

#endif // ETHERNETDOWNLOAD_H
