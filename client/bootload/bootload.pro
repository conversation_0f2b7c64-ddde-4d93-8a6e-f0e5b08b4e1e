QT       += core gui concurrent network

greaterThan(QT_MAJOR_VERSION, 4): QT += widgets

CONFIG += c++11

# 定义警告对于使用已弃用的Qt特性
DEFINES += QT_DEPRECATED_WARNINGS

SOURCES += \
    main.cpp \
    mainwindow.cpp \
    candownload.cpp \
    ethernetdownload.cpp

HEADERS += \
    mainwindow.h \
    candownload.h \
    ethernetdownload.h \
    version.h \
    common_types.h

FORMS += \
    mainwindow.ui \
    candownload.ui \
    ethernetdownload.ui

# 针对不同平台的部署规则
qnx: target.path = /tmp/$${TARGET}/bin
else: unix:!android: target.path = /opt/$${TARGET}/bin
!isEmpty(target.path): INSTALLS += target

# 包含目录和库目录设置为项目目录，适用于本地开发
INCLUDEPATH += $$PWD
INCLUDEPATH += $$PWD/libs
LIBS += -L$$PWD/libs -lControlCAN

DISTFILES += \
    README.txt

RESOURCES += \
    resources.qrc

