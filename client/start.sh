#!/bin/bash

# RK3588客户端启动脚本

set -e

echo "=========================================="
echo "RK3588客户端启动脚本"
echo "=========================================="

# 检查Docker是否安装
if ! command -v docker &> /dev/null; then
    echo "错误: Docker未安装，请先安装Docker"
    exit 1
fi

# 检查docker-compose是否安装
if ! command -v docker-compose &> /dev/null; then
    echo "错误: docker-compose未安装，请先安装docker-compose"
    exit 1
fi

# 进入脚本所在目录
cd "$(dirname "$0")"

# 创建必要的目录
echo "创建必要的目录..."
mkdir -p logs
mkdir -p data

# 检查配置文件
if [ ! -f "config.json" ]; then
    echo "警告: config.json不存在，将使用默认配置"
fi

# 停止现有容器（如果存在）
echo "停止现有容器..."
docker-compose down --remove-orphans || true

# 构建镜像
echo "构建Docker镜像..."
docker-compose build

# 启动服务
echo "启动客户端服务..."
docker-compose up -d

# 等待服务启动
echo "等待服务启动..."
sleep 5

# 检查服务状态
echo "检查服务状态..."
docker-compose ps

# 显示日志
echo "显示最近的日志..."
docker-compose logs --tail=20

echo "=========================================="
echo "客户端启动完成！"
echo ""
echo "Web界面: http://localhost:8000"
echo "健康检查: http://localhost:8000/health"
echo ""
echo "查看日志: docker-compose logs -f"
echo "停止服务: docker-compose down"
echo "=========================================="
