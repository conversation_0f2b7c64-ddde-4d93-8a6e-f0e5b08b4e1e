/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f5f5f5;
    color: #333;
    line-height: 1.6;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    background: white;
    min-height: 100vh;
    box-shadow: 0 0 20px rgba(0,0,0,0.1);
}

/* 头部样式 */
.header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    text-align: center;
}

/* 主工具栏样式 */
.main-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    padding: 12px 16px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #dee2e6;
    gap: 20px;
}

.status-section {
    display: flex;
    align-items: center;
    gap: 15px;
    flex: 1;
}

.status-section #device-id {
    font-size: 1.1em;
    font-weight: 600;
    color: #333;
}

.status-section #param-count,
.status-section #last-update {
    font-size: 0.9em;
    color: #666;
}

.status-indicator {
    font-size: 16px;
    line-height: 1;
    /* 去掉椭圆背景样式 */
}

/* 工具栏样式 */
.toolbar {
    padding: 20px;
    background-color: #fafafa;
    border-bottom: 1px solid #e0e0e0;
    flex-wrap: wrap;
}

.btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
    text-decoration: none;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.btn-primary {
    background-color: #2196F3;
    color: white;
}

.btn-primary:hover {
    background-color: #1976D2;
}

.btn-secondary {
    background-color: #757575;
    color: white;
}

.btn-secondary:hover {
    background-color: #616161;
}

.btn-small {
    padding: 8px 12px;
    font-size: 12px;
}

#log-manager-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    margin-left: 10px;
}

#log-manager-btn:hover {
    background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.search-box {
    display: flex;
    align-items: center;
    gap: 5px;
}

.search-box input {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    width: 200px;
}

/* 内容区域样式 */
.content {
    padding: 20px;
    min-height: 400px;
}

.loading {
    text-align: center;
    padding: 40px;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #2196F3;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.error-message {
    background-color: #ffebee;
    border: 1px solid #f44336;
    border-radius: 6px;
    padding: 15px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
    color: #c62828;
}

.error-close {
    margin-left: auto;
    background: none;
    border: none;
    font-size: 20px;
    cursor: pointer;
    color: #c62828;
}

.no-data {
    text-align: center;
    padding: 60px 20px;
    color: #666;
}

.no-data p {
    margin-bottom: 10px;
    font-size: 16px;
}

/* 参数网格样式 */
.parameters-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
}

.parameter-item {
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 20px;
    transition: all 0.3s ease;
}

.parameter-item:hover {
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    transform: translateY(-2px);
}

.parameter-name {
    font-weight: bold;
    font-size: 16px;
    color: #333;
    margin-bottom: 10px;
    word-break: break-all;
}

.parameter-value {
    display: flex;
    align-items: center;
    gap: 10px;
}

.parameter-buttons {
    display: flex;
    gap: 5px;
}

.parameter-input {
    flex: 1;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    transition: border-color 0.3s ease;
}

.parameter-input:focus {
    outline: none;
    border-color: #2196F3;
    box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.2);
}

.parameter-input.modified {
    border-color: #ff9800;
    background-color: #fff3e0;
}

.read-btn {
    padding: 8px 12px;
    background-color: #2196F3;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    transition: background-color 0.3s ease;
    min-width: 50px;
}

.read-btn:hover {
    background-color: #1976D2;
}

.read-btn:disabled {
    background-color: #ccc;
    cursor: not-allowed;
}

.update-btn {
    padding: 8px 12px;
    background-color: #4CAF50;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    transition: background-color 0.3s ease;
    min-width: 50px;
}

.update-btn:hover {
    background-color: #45a049;
}

.update-btn:disabled {
    background-color: #ccc;
    cursor: not-allowed;
}

/* 底部样式 */
.footer {
    background-color: #f5f5f5;
    padding: 15px 20px;
    border-top: 1px solid #e0e0e0;
    text-align: center;
    color: #666;
    font-size: 14px;
}

.footer-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

/* 模态框样式 */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal-content {
    background: white;
    padding: 30px;
    border-radius: 8px;
    max-width: 400px;
    width: 90%;
    text-align: center;
}

.modal-content h3 {
    margin-bottom: 20px;
    color: #333;
}

.modal-content p {
    margin-bottom: 20px;
    text-align: left;
    line-height: 1.8;
}

.modal-buttons {
    display: flex;
    gap: 10px;
    justify-content: center;
}

/* 工具类 */
.hidden {
    display: none !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        margin: 0;
        box-shadow: none;
    }

    .header h1 {
        font-size: 2em;
    }

    .status-bar {
        flex-direction: column;
        gap: 10px;
    }

    .main-toolbar {
        flex-direction: column;
        align-items: stretch;
        gap: 15px;
    }

    .status-section {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }

    .search-box input {
        width: 100%;
    }

    .parameters-grid {
        grid-template-columns: 1fr;
    }

    .footer-info {
        flex-direction: column;
        gap: 5px;
    }
}

/* 文件上传对话框样式 */
.upload-area {
    margin: 20px 0;
}

.drop-zone {
    border: 2px dashed #ddd;
    border-radius: 8px;
    padding: 40px 20px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background-color: #fafafa;
}

.drop-zone:hover {
    border-color: #007bff;
    background-color: #f0f8ff;
}

.drop-zone.dragover {
    border-color: #007bff;
    background-color: #e6f3ff;
}

.drop-zone-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
}

.upload-icon {
    font-size: 48px;
    opacity: 0.6;
}

.drop-zone p {
    margin: 0;
    color: #666;
}

.file-hint {
    font-size: 12px;
    color: #999;
}

.file-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    background-color: #f8f9fa;
    border-radius: 4px;
    margin-top: 10px;
}

.file-info span {
    font-size: 14px;
    color: #666;
}

/* 参数映射显示样式 */
.param-mapping {
    color: #28a745;
    font-size: 16px;
    font-style: italic;
    margin-left: 8px;
    font-weight: 500;
}
