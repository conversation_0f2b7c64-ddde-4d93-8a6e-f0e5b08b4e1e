// 配置管理页面JavaScript

// 默认配置值
const DEFAULT_CONFIG = {
    WEB_PORT: '7002',
    UDP_PORT: '18001',
    SERVER_HOST: '*************',
    SERVER_PORT: '8888',
    DEVICE_ID: '太阳能通信车001',
    UDP_TARGET_IP: '*************',
    UDP_TARGET_PORT: '8080'
};

// DOM元素
const elements = {
    backBtn: document.getElementById('back-btn'),
    configForm: document.getElementById('config-form'),
    saveBtn: document.getElementById('save-btn'),
    resetBtn: document.getElementById('reset-btn'),
    restartServiceBtn: document.getElementById('restart-service-btn'),
    restartSystemBtn: document.getElementById('restart-system-btn'),
    adminPassword: document.getElementById('admin-password'),
    customServerDisplay: document.getElementById('custom-server-display'),
    customServerStatus: document.getElementById('custom-server-status'),
    defaultServerStatus: document.getElementById('default-server-status'),
    connectionSummary: document.getElementById('connection-summary'),
    messageContainer: document.getElementById('message-container')
};

// 初始化
document.addEventListener('DOMContentLoaded', function () {
    initializeEventListeners();
    loadCurrentConfig();
    checkServerStatus(); // 立即检查一次服务器状态
});

function initializeEventListeners() {
    elements.backBtn.addEventListener('click', () => {
        window.location.href = '/';
    });

    elements.saveBtn.addEventListener('click', saveConfig);
    elements.resetBtn.addEventListener('click', resetToDefaults);
    elements.restartServiceBtn.addEventListener('click', restartService);
    elements.restartSystemBtn.addEventListener('click', restartSystem);

    // 监听服务器配置变化
    document.getElementById('SERVER_HOST').addEventListener('input', updateServerDisplay);
    document.getElementById('SERVER_PORT').addEventListener('input', updateServerDisplay);

    // 定期检查服务器状态
    setInterval(checkServerStatus, 10000); // 每10秒检查一次
}

// 加载当前配置
async function loadCurrentConfig() {
    try {
        const response = await fetch('/api/config');
        if (response.ok) {
            const data = await response.json();
            if (data.success) {
                populateForm(data.data);
                updateServerDisplay();
            } else {
                showMessage('加载配置失败: ' + data.message, 'error');
                // 使用默认值
                populateForm(DEFAULT_CONFIG);
            }
        } else {
            throw new Error('HTTP ' + response.status);
        }
    } catch (error) {
        console.error('加载配置失败:', error);
        showMessage('加载配置失败，使用默认值', 'warning');
        populateForm(DEFAULT_CONFIG);
    }
}

// 填充表单
function populateForm(config) {
    Object.keys(DEFAULT_CONFIG).forEach(key => {
        const element = document.getElementById(key);
        if (element) {
            element.value = config[key] || DEFAULT_CONFIG[key];
        }
    });
}

// 更新服务器显示
function updateServerDisplay() {
    const host = document.getElementById('SERVER_HOST').value || DEFAULT_CONFIG.SERVER_HOST;
    const port = document.getElementById('SERVER_PORT').value || DEFAULT_CONFIG.SERVER_PORT;

    elements.customServerDisplay.textContent = `自定义服务器: ${host}:${port}`;

    if (host && port) {
        elements.customServerStatus.textContent = '已配置';
        elements.customServerStatus.className = 'server-status active';
    } else {
        elements.customServerStatus.textContent = '未配置';
        elements.customServerStatus.className = 'server-status inactive';
    }
}

// 保存配置
async function saveConfig() {
    try {
        elements.saveBtn.disabled = true;
        elements.saveBtn.textContent = '保存中...';

        // 收集表单数据
        const config = {};
        Object.keys(DEFAULT_CONFIG).forEach(key => {
            const element = document.getElementById(key);
            if (element) {
                config[key] = element.value || DEFAULT_CONFIG[key];
            }
        });

        // 验证配置
        if (!validateConfig(config)) {
            return;
        }

        const response = await fetch('/api/config', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(config)
        });

        const data = await response.json();

        if (data.success) {
            showMessage('配置保存成功，应用配置已更新', 'success');
            updateServerDisplay();

            // 延迟2秒后提示用户可以返回主页查看更新
            setTimeout(() => {
                showMessage('配置已生效，可返回主页查看更新', 'info');
            }, 2000);
        } else {
            showMessage('保存配置失败: ' + data.message, 'error');
        }
    } catch (error) {
        console.error('保存配置失败:', error);
        showMessage('保存配置失败: ' + error.message, 'error');
    } finally {
        elements.saveBtn.disabled = false;
        elements.saveBtn.textContent = '保存配置';
    }
}

// 重置为默认值
function resetToDefaults() {
    if (confirm('确定要重置为默认配置吗？')) {
        populateForm(DEFAULT_CONFIG);
        updateServerDisplay();
        showMessage('已重置为默认配置', 'info');
    }
}

// 验证配置
function validateConfig(config) {
    // 验证端口范围
    const ports = ['WEB_PORT', 'UDP_PORT', 'SERVER_PORT', 'UDP_TARGET_PORT'];
    for (const portKey of ports) {
        const port = parseInt(config[portKey]);
        if (isNaN(port) || port < 1000 || port > 65535) {
            showMessage(`${portKey} 必须是1000-65535之间的数字`, 'error');
            return false;
        }
    }

    // 验证IP地址格式
    const ipKeys = ['SERVER_HOST', 'UDP_TARGET_IP'];
    const ipRegex = /^(\d{1,3}\.){3}\d{1,3}$/;
    for (const ipKey of ipKeys) {
        const ip = config[ipKey];
        if (ip && !ipRegex.test(ip)) {
            showMessage(`${ipKey} 格式不正确`, 'error');
            return false;
        }
    }

    // 验证设备ID不为空
    if (!config.DEVICE_ID || config.DEVICE_ID.trim() === '') {
        showMessage('设备ID不能为空', 'error');
        return false;
    }

    return true;
}

// 显示消息
function showMessage(message, type = 'info') {
    const messageDiv = document.createElement('div');
    messageDiv.className = `message message-${type}`;
    messageDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 12px 20px;
        border-radius: 4px;
        color: white;
        font-weight: 500;
        z-index: 1000;
        max-width: 300px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.2);
    `;

    switch (type) {
        case 'success':
            messageDiv.style.backgroundColor = '#28a745';
            break;
        case 'error':
            messageDiv.style.backgroundColor = '#dc3545';
            break;
        case 'warning':
            messageDiv.style.backgroundColor = '#ffc107';
            messageDiv.style.color = '#212529';
            break;
        default:
            messageDiv.style.backgroundColor = '#17a2b8';
    }

    messageDiv.textContent = message;
    elements.messageContainer.appendChild(messageDiv);

    setTimeout(() => {
        if (messageDiv.parentNode) {
            messageDiv.parentNode.removeChild(messageDiv);
        }
    }, 3000);
}

// 检查服务器状态
async function checkServerStatus() {
    try {
        const response = await fetch('/api/server-status');
        if (response.ok) {
            const data = await response.json();
            if (data.success) {
                updateServerStatusDisplay(data.data);
            }
        }
    } catch (error) {
        console.error('检查服务器状态失败:', error);
    }
}

// 更新服务器状态显示
function updateServerStatusDisplay(statusData) {
    const { servers, connected_count } = statusData;

    // 更新各服务器状态
    servers.forEach(server => {
        if (server.name === 'default') {
            elements.defaultServerStatus.textContent = server.connected ? '已连接' : '未连接';
            elements.defaultServerStatus.className = server.connected ? 'server-status active' : 'server-status inactive';
        } else if (server.name === 'custom') {
            const statusText = server.connected ? '已连接' : '未连接';
            elements.customServerStatus.textContent = statusText;
            elements.customServerStatus.className = server.connected ? 'server-status active' : 'server-status inactive';
        }
    });

    // 更新连接摘要
    const totalServers = servers.length;
    elements.connectionSummary.textContent = `${connected_count}/${totalServers} 服务器已连接`;
    elements.connectionSummary.className = connected_count > 0 ? 'server-status active' : 'server-status inactive';
}

// 重启服务
async function restartService() {
    if (!confirm('确定要重启参数设置客户端服务吗？\n\n重启后需要等待几秒钟服务才能恢复正常。')) {
        return;
    }

    const password = elements.adminPassword.value.trim();
    if (!password) {
        showMessage('请输入管理员密码', 'error');
        elements.adminPassword.focus();
        return;
    }

    try {
        elements.restartServiceBtn.disabled = true;
        elements.restartServiceBtn.textContent = '重启中...';

        const response = await fetch('/api/restart-service', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ password: password })
        });

        const data = await response.json();

        if (data.success) {
            showMessage('服务重启命令已发送，页面将在5秒后刷新', 'success');

            // 5秒后刷新页面
            setTimeout(() => {
                window.location.reload();
            }, 5000);
        } else {
            showMessage('重启服务失败: ' + data.message, 'error');
        }
    } catch (error) {
        console.error('重启服务失败:', error);
        showMessage('重启服务失败: ' + error.message, 'error');
    } finally {
        // 延迟恢复按钮状态，避免在重启过程中被点击
        setTimeout(() => {
            elements.restartServiceBtn.disabled = false;
            elements.restartServiceBtn.textContent = '重启服务';
        }, 3000);
    }
}

// 重启计算机
async function restartSystem() {
    const password = elements.adminPassword.value.trim();
    if (!password) {
        showMessage('请输入管理员密码', 'error');
        elements.adminPassword.focus();
        return;
    }

    if (!confirm('确定要重启计算机吗？\n\n⚠️ 警告：这将重启整个系统，所有未保存的工作将丢失！\n\n重启后需要等待系统完全启动才能重新访问。')) {
        return;
    }

    // 二次确认
    if (!confirm('最后确认：真的要重启计算机吗？')) {
        return;
    }

    try {
        elements.restartSystemBtn.disabled = true;
        elements.restartSystemBtn.textContent = '重启中...';

        const response = await fetch('/api/restart-system', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ password: password })
        });

        const data = await response.json();

        if (data.success) {
            showMessage('系统重启命令已发送，计算机将在几秒钟内重启', 'success');

            // 显示倒计时提示
            let countdown = 10;
            const countdownInterval = setInterval(() => {
                showMessage(`系统将在 ${countdown} 秒内重启...`, 'warning');
                countdown--;
                if (countdown < 0) {
                    clearInterval(countdownInterval);
                    showMessage('系统正在重启，请等待...', 'info');
                }
            }, 1000);
        } else {
            showMessage('重启系统失败: ' + data.message, 'error');
            elements.restartSystemBtn.disabled = false;
            elements.restartSystemBtn.textContent = '重启计算机';
        }
    } catch (error) {
        console.error('重启系统失败:', error);
        showMessage('重启系统失败: ' + error.message, 'error');
        elements.restartSystemBtn.disabled = false;
        elements.restartSystemBtn.textContent = '重启计算机';
    }
}
