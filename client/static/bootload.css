/* 固件下载页面专用样式 */

/* 模式切换 */
.mode-switch-container {
    padding: 20px 0;
    text-align: center;
    border-bottom: 1px solid #e0e0e0;
    margin-bottom: 20px;
}

.mode-switch {
    display: inline-flex;
    background: #f5f5f5;
    border-radius: 8px;
    padding: 4px;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
}

.mode-btn {
    padding: 12px 24px;
    border: none;
    background: transparent;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
    color: #666;
}

.mode-btn.active {
    background: #007bff;
    color: white;
    box-shadow: 0 2px 4px rgba(0, 123, 255, 0.3);
}

.mode-btn:hover:not(.active) {
    background: #e9ecef;
    color: #333;
}

/* 下载面板 */
.download-panel {
    display: none;
    background: white;
    border-radius: 12px;
    padding: 24px;
    margin-bottom: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: 1px solid #e0e0e0;
}

.download-panel.active {
    display: block;
}

.panel-header {
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 2px solid #f0f0f0;
}

.panel-header h2 {
    margin: 0;
    color: #333;
    font-size: 20px;
    font-weight: 600;
}

/* 配置区域 */
.config-section {
    margin-bottom: 24px;
}

.config-row {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
    gap: 12px;
}

.config-row label {
    min-width: 100px;
    font-weight: 500;
    color: #555;
    text-align: right;
}

.config-row select,
.config-row input[type="text"],
.config-row input[type="number"] {
    flex: 1;
    max-width: 200px;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.3s ease;
}

.config-row select:focus,
.config-row input:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.1);
}

.btn-small {
    padding: 6px 12px;
    font-size: 12px;
    margin-left: 8px;
}

/* 操作按钮区域 */
.action-section {
    display: flex;
    gap: 12px;
    justify-content: center;
    padding: 20px 0;
    border-top: 1px solid #f0f0f0;
}

.action-section .btn {
    min-width: 100px;
    padding: 12px 24px;
    font-size: 14px;
    font-weight: 500;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.btn-primary {
    background: #007bff;
    color: white;
    border: 1px solid #007bff;
}

.btn-primary:hover:not(:disabled) {
    background: #0056b3;
    border-color: #0056b3;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 123, 255, 0.3);
}

.btn-success {
    background: #28a745;
    color: white;
    border: 1px solid #28a745;
}

.btn-success:hover:not(:disabled) {
    background: #1e7e34;
    border-color: #1e7e34;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(40, 167, 69, 0.3);
}

.btn-warning {
    background: #ffc107;
    color: #212529;
    border: 1px solid #ffc107;
}

.btn-warning:hover:not(:disabled) {
    background: #e0a800;
    border-color: #e0a800;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(255, 193, 7, 0.3);
}

.btn-secondary {
    background: #6c757d;
    color: white;
    border: 1px solid #6c757d;
}

.btn-secondary:hover {
    background: #545b62;
    border-color: #545b62;
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none !important;
    box-shadow: none !important;
}

/* 进度显示 */
.progress-section {
    margin-bottom: 20px;
}

.progress-container {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.progress-bar {
    flex: 1;
    height: 20px;
    background: #e9ecef;
    border-radius: 10px;
    overflow: hidden;
    position: relative;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #007bff, #0056b3);
    border-radius: 10px;
    width: 0%;
    transition: width 0.3s ease;
    position: relative;
}

.progress-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: progress-shine 2s infinite;
}

@keyframes progress-shine {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

#progress-text {
    font-weight: 600;
    color: #007bff;
    min-width: 50px;
    text-align: right;
}

/* 日志区域 */
.log-section {
    background: white;
    border-radius: 12px;
    border: 1px solid #e0e0e0;
    overflow: hidden;
}

.log-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    background: #f8f9fa;
    border-bottom: 1px solid #e0e0e0;
}

.log-header h3 {
    margin: 0;
    color: #333;
    font-size: 16px;
    font-weight: 600;
}

.log-container {
    height: 300px;
    overflow-y: auto;
    padding: 16px 20px;
    background: #fafafa;
    font-family: 'Consolas', 'Monaco', monospace;
    font-size: 13px;
    line-height: 1.5;
}

.log-item {
    margin-bottom: 8px;
    padding: 6px 12px;
    background: white;
    border-radius: 4px;
    border-left: 3px solid #007bff;
    color: #333;
    word-wrap: break-word;
}

.log-item.error {
    border-left-color: #dc3545;
    background: #fff5f5;
    color: #721c24;
}

.log-item.warning {
    border-left-color: #ffc107;
    background: #fffbf0;
    color: #856404;
}

.log-item.success {
    border-left-color: #28a745;
    background: #f0fff4;
    color: #155724;
}

/* 文件选择对话框 */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal.hidden {
    display: none;
}

.modal-content {
    background: white;
    border-radius: 12px;
    width: 90%;
    max-width: 500px;
    max-height: 80vh;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px;
    border-bottom: 1px solid #e0e0e0;
    background: #f8f9fa;
}

.modal-header h3 {
    margin: 0;
    color: #333;
    font-size: 18px;
    font-weight: 600;
}

.modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #666;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.modal-close:hover {
    background: #e9ecef;
    color: #333;
}

.modal-body {
    padding: 24px;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding: 20px 24px;
    border-top: 1px solid #e0e0e0;
    background: #f8f9fa;
}

/* 文件拖拽区域 */
.file-drop-zone {
    border: 2px dashed #ddd;
    border-radius: 8px;
    padding: 40px 20px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #fafafa;
}

.file-drop-zone:hover,
.file-drop-zone.dragover {
    border-color: #007bff;
    background: #f0f8ff;
}

.drop-zone-content {
    pointer-events: none;
}

.drop-zone-icon {
    font-size: 48px;
    margin-bottom: 16px;
}

.file-hint {
    color: #666;
    font-size: 12px;
    margin-top: 8px;
}

.file-info {
    margin-top: 16px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.file-info.hidden {
    display: none;
}

.file-details {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .config-row {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }
    
    .config-row label {
        min-width: auto;
        text-align: left;
    }
    
    .config-row select,
    .config-row input {
        max-width: 100%;
        width: 100%;
    }
    
    .action-section {
        flex-direction: column;
    }
    
    .action-section .btn {
        width: 100%;
    }
    
    .progress-container {
        flex-direction: column;
        gap: 8px;
    }
    
    .modal-content {
        width: 95%;
        margin: 20px;
    }
}
