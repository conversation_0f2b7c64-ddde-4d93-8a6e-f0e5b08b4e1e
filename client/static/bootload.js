// 固件下载页面JavaScript
class BootloadManager {
    constructor() {
        this.currentMode = 'can'; // 'can' 或 'ethernet'
        this.isConnected = false;
        this.isDownloading = false;
        this.firmwareFile = null;
        this.downloadProgress = 0;

        // 配置参数
        this.config = {
            can: {
                baudrate: '500K',
                codeArea: 1,
                comId: '0x123'
            },
            ethernet: {
                localIp: 'auto',
                localPort: 7811,
                targetIp: '*************',
                targetPort: 7812,
                codeArea: 1,
                paraTargetIp: '*************'
            }
        };

        this.initializeElements();
        this.initializeEventListeners();
        this.initializeNetworkInterfaces();
        this.addLogMessage('固件下载工具已启动');
    }

    initializeElements() {
        // 模式切换按钮
        this.canModeBtn = document.getElementById('can-mode-btn');
        this.ethernetModeBtn = document.getElementById('ethernet-mode-btn');

        // 面板
        this.canPanel = document.getElementById('can-download-panel');
        this.ethernetPanel = document.getElementById('ethernet-download-panel');

        // CAN配置元素
        this.canBaudrate = document.getElementById('can-baudrate');
        this.canCodeArea = document.getElementById('can-code-area');
        this.canComId = document.getElementById('can-com-id');
        this.canConnectBtn = document.getElementById('can-connect-btn');
        this.canDownloadBtn = document.getElementById('can-download-btn');
        this.canClearFlagBtn = document.getElementById('can-clear-flag-btn');

        // 以太网配置元素
        this.ethernetLocalIp = document.getElementById('ethernet-local-ip');
        this.ethernetLocalPort = document.getElementById('ethernet-local-port');
        this.ethernetTargetIp = document.getElementById('ethernet-target-ip');
        this.ethernetTargetPort = document.getElementById('ethernet-target-port');
        this.ethernetCodeArea = document.getElementById('ethernet-code-area');
        this.ethernetParaTargetIp = document.getElementById('ethernet-para-target-ip');
        this.ethernetModifyParaIpBtn = document.getElementById('ethernet-modify-para-ip-btn');
        this.ethernetConnectBtn = document.getElementById('ethernet-connect-btn');
        this.ethernetDownloadBtn = document.getElementById('ethernet-download-btn');
        this.ethernetClearFlagBtn = document.getElementById('ethernet-clear-flag-btn');

        // 进度元素
        this.progressFill = document.getElementById('progress-fill');
        this.progressText = document.getElementById('progress-text');

        // 日志元素
        this.logContainer = document.getElementById('log-container');
        this.clearLogBtn = document.getElementById('clear-log-btn');

        // 文件对话框元素
        this.fileDialog = document.getElementById('file-dialog');
        this.fileDropZone = document.getElementById('file-drop-zone');
        this.firmwareFileInput = document.getElementById('firmware-file-input');
        this.fileInfo = document.getElementById('file-info');
        this.fileName = document.getElementById('file-name');
        this.fileSize = document.getElementById('file-size');
        this.fileConfirmBtn = document.getElementById('file-confirm-btn');
        this.fileCancelBtn = document.getElementById('file-cancel-btn');
        this.fileDialogClose = document.getElementById('file-dialog-close');

        // 其他元素
        this.backBtn = document.getElementById('back-btn');
        this.connectionStatus = document.getElementById('connection-status');
    }

    initializeEventListeners() {
        // 模式切换
        this.canModeBtn.addEventListener('click', () => this.switchMode('can'));
        this.ethernetModeBtn.addEventListener('click', () => this.switchMode('ethernet'));

        // CAN事件
        this.canBaudrate.addEventListener('change', (e) => this.updateCanConfig('baudrate', e.target.value));
        this.canCodeArea.addEventListener('change', (e) => this.updateCanConfig('codeArea', parseInt(e.target.value)));
        this.canComId.addEventListener('change', (e) => this.updateCanConfig('comId', e.target.value));
        this.canConnectBtn.addEventListener('click', () => this.handleConnect('can'));
        this.canDownloadBtn.addEventListener('click', () => this.handleDownload('can'));
        this.canClearFlagBtn.addEventListener('click', () => this.handleClearFlag('can'));

        // 以太网事件
        this.ethernetLocalIp.addEventListener('change', (e) => this.updateEthernetConfig('localIp', e.target.value));
        this.ethernetLocalPort.addEventListener('change', (e) => this.updateEthernetConfig('localPort', parseInt(e.target.value)));
        this.ethernetTargetIp.addEventListener('change', (e) => this.updateEthernetConfig('targetIp', e.target.value));
        this.ethernetTargetPort.addEventListener('change', (e) => this.updateEthernetConfig('targetPort', parseInt(e.target.value)));
        this.ethernetCodeArea.addEventListener('change', (e) => this.updateEthernetConfig('codeArea', parseInt(e.target.value)));
        this.ethernetParaTargetIp.addEventListener('change', (e) => this.updateEthernetConfig('paraTargetIp', e.target.value));
        this.ethernetModifyParaIpBtn.addEventListener('click', () => this.handleModifyParaTargetIp());
        this.ethernetConnectBtn.addEventListener('click', () => this.handleConnect('ethernet'));
        this.ethernetDownloadBtn.addEventListener('click', () => this.handleDownload('ethernet'));
        this.ethernetClearFlagBtn.addEventListener('click', () => this.handleClearFlag('ethernet'));

        // 文件相关事件
        this.fileDropZone.addEventListener('click', () => this.firmwareFileInput.click());
        this.fileDropZone.addEventListener('dragover', (e) => this.handleDragOver(e));
        this.fileDropZone.addEventListener('drop', (e) => this.handleFileDrop(e));
        this.firmwareFileInput.addEventListener('change', (e) => this.handleFileSelect(e));
        this.fileConfirmBtn.addEventListener('click', () => this.confirmFileSelection());
        this.fileCancelBtn.addEventListener('click', () => this.hideFileDialog());
        this.fileDialogClose.addEventListener('click', () => this.hideFileDialog());

        // 其他事件
        this.clearLogBtn.addEventListener('click', () => this.clearLog());
        this.backBtn.addEventListener('click', () => this.goBack());
    }

    // 模式切换
    switchMode(mode) {
        this.currentMode = mode;

        // 更新按钮状态
        this.canModeBtn.classList.toggle('active', mode === 'can');
        this.ethernetModeBtn.classList.toggle('active', mode === 'ethernet');

        // 更新面板显示
        this.canPanel.classList.toggle('active', mode === 'can');
        this.ethernetPanel.classList.toggle('active', mode === 'ethernet');

        // 重置连接状态
        this.isConnected = false;
        this.updateConnectionStatus();
        this.updateButtonStates();

        this.addLogMessage(`切换到${mode === 'can' ? 'CAN' : '以太网'}下载模式`);
    }

    // 更新配置
    updateCanConfig(key, value) {
        this.config.can[key] = value;
        this.addLogMessage(`CAN配置更新: ${key} = ${value}`);
    }

    updateEthernetConfig(key, value) {
        this.config.ethernet[key] = value;
        this.addLogMessage(`以太网配置更新: ${key} = ${value}`);
    }

    // 初始化网络接口
    async initializeNetworkInterfaces() {
        try {
            const response = await fetch('/api/bootload/network-interfaces');
            const interfaces = await response.json();

            // 清空现有选项
            this.ethernetLocalIp.innerHTML = '<option value="auto">自动检测</option>';

            // 添加网络接口选项
            interfaces.forEach(iface => {
                const option = document.createElement('option');
                option.value = iface.ip;
                option.textContent = `${iface.name} (${iface.ip})`;
                this.ethernetLocalIp.appendChild(option);
            });

        } catch (error) {
            this.addLogMessage('获取网络接口失败: ' + error.message, 'error');
        }
    }

    // 连接处理
    async handleConnect(mode) {
        if (this.isConnected) {
            // 断开连接
            await this.disconnect(mode);
        } else {
            // 建立连接
            await this.connect(mode);
        }
    }

    async connect(mode) {
        this.addLogMessage(`正在连接${mode === 'can' ? 'CAN总线' : '以太网'}...`);

        try {
            const config = this.config[mode];
            const response = await fetch('/api/bootload/connect', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    mode: mode,
                    config: config
                })
            });

            const result = await response.json();

            if (result.success) {
                this.isConnected = true;
                this.addLogMessage('连接成功', 'success');

                // 读取版本信息
                await this.readVersions();
            } else {
                this.addLogMessage('连接失败: ' + result.message, 'error');
            }

        } catch (error) {
            this.addLogMessage('连接异常: ' + error.message, 'error');
        }

        this.updateConnectionStatus();
        this.updateButtonStates();
    }

    async disconnect(mode) {
        this.addLogMessage(`正在断开${mode === 'can' ? 'CAN总线' : '以太网'}连接...`);

        try {
            const response = await fetch('/api/bootload/disconnect', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ mode: mode })
            });

            const result = await response.json();

            if (result.success) {
                this.isConnected = false;
                this.addLogMessage('断开连接成功', 'success');
            } else {
                this.addLogMessage('断开连接失败: ' + result.message, 'error');
            }

        } catch (error) {
            this.addLogMessage('断开连接异常: ' + error.message, 'error');
        }

        this.updateConnectionStatus();
        this.updateButtonStates();
    }

    // 读取版本信息
    async readVersions() {
        for (let region = 1; region <= 3; region++) {
            try {
                const response = await fetch('/api/bootload/read-version', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        mode: this.currentMode,
                        region: region
                    })
                });

                const result = await response.json();

                if (result.success) {
                    this.addLogMessage(`区域${region}版本: ${result.version}`, 'success');
                } else {
                    this.addLogMessage(`读取区域${region}版本失败: ${result.message}`, 'warning');
                }

            } catch (error) {
                this.addLogMessage(`读取区域${region}版本异常: ${error.message}`, 'error');
            }
        }
    }

    // 下载处理
    async handleDownload(mode) {
        if (!this.isConnected) {
            this.addLogMessage('请先建立连接', 'warning');
            return;
        }

        this.showFileDialog();
    }

    // 清标志位处理
    async handleClearFlag(mode) {
        if (!this.isConnected) {
            this.addLogMessage('请先建立连接', 'warning');
            return;
        }

        this.addLogMessage('正在清除标志位...');

        try {
            const response = await fetch('/api/bootload/clear-flag', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    mode: mode,
                    codeArea: this.config[mode].codeArea
                })
            });

            const result = await response.json();

            if (result.success) {
                this.addLogMessage('清除标志位成功', 'success');
            } else {
                this.addLogMessage('清除标志位失败: ' + result.message, 'error');
            }

        } catch (error) {
            this.addLogMessage('清除标志位异常: ' + error.message, 'error');
        }
    }

    // 修改参数目标IP
    async handleModifyParaTargetIp() {
        const newIp = this.ethernetParaTargetIp.value;

        if (!this.validateIpAddress(newIp)) {
            this.addLogMessage('IP地址格式不正确', 'error');
            return;
        }

        this.addLogMessage(`正在修改参数目标IP为: ${newIp}`);

        try {
            const response = await fetch('/api/bootload/modify-para-target-ip', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    newIp: newIp
                })
            });

            const result = await response.json();

            if (result.success) {
                this.addLogMessage('修改参数目标IP成功', 'success');
                this.config.ethernet.paraTargetIp = newIp;
            } else {
                this.addLogMessage('修改参数目标IP失败: ' + result.message, 'error');
            }

        } catch (error) {
            this.addLogMessage('修改参数目标IP异常: ' + error.message, 'error');
        }
    }

    // 工具函数
    validateIpAddress(ip) {
        const ipRegex = /^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
        return ipRegex.test(ip);
    }

    updateConnectionStatus() {
        this.connectionStatus.textContent = this.isConnected ? '🟢' : '🔴';
        this.connectionStatus.className = `status-indicator ${this.isConnected ? 'online' : 'offline'}`;
    }

    updateButtonStates() {
        const connectBtn = this.currentMode === 'can' ? this.canConnectBtn : this.ethernetConnectBtn;
        const downloadBtn = this.currentMode === 'can' ? this.canDownloadBtn : this.ethernetDownloadBtn;
        const clearFlagBtn = this.currentMode === 'can' ? this.canClearFlagBtn : this.ethernetClearFlagBtn;

        connectBtn.textContent = this.isConnected ? '断开' : '连接';
        downloadBtn.disabled = !this.isConnected || this.isDownloading;
        clearFlagBtn.disabled = !this.isConnected || this.isDownloading;
    }

    addLogMessage(message, type = 'info') {
        const logItem = document.createElement('div');
        logItem.className = `log-item ${type}`;

        const timestamp = new Date().toLocaleTimeString();
        logItem.textContent = `[${timestamp}] ${message}`;

        this.logContainer.appendChild(logItem);
        this.logContainer.scrollTop = this.logContainer.scrollHeight;

        // 限制日志条数
        const logItems = this.logContainer.querySelectorAll('.log-item');
        if (logItems.length > 1000) {
            logItems[0].remove();
        }
    }

    clearLog() {
        this.logContainer.innerHTML = '';
        this.addLogMessage('日志已清空');
    }

    goBack() {
        window.location.href = '/';
    }

    // 文件处理
    showFileDialog() {
        this.fileDialog.classList.remove('hidden');
        this.firmwareFile = null;
        this.fileInfo.classList.add('hidden');
        this.fileConfirmBtn.disabled = true;
    }

    hideFileDialog() {
        this.fileDialog.classList.add('hidden');
    }

    handleDragOver(e) {
        e.preventDefault();
        this.fileDropZone.classList.add('dragover');
    }

    handleFileDrop(e) {
        e.preventDefault();
        this.fileDropZone.classList.remove('dragover');

        const files = e.dataTransfer.files;
        if (files.length > 0) {
            this.processFile(files[0]);
        }
    }

    handleFileSelect(e) {
        const files = e.target.files;
        if (files.length > 0) {
            this.processFile(files[0]);
        }
    }

    processFile(file) {
        // 检查文件类型
        const allowedTypes = ['.bin', '.hex'];
        const fileExtension = '.' + file.name.split('.').pop().toLowerCase();

        if (!allowedTypes.includes(fileExtension)) {
            this.addLogMessage('不支持的文件格式，请选择.bin或.hex文件', 'error');
            return;
        }

        this.firmwareFile = file;
        this.fileName.textContent = file.name;
        this.fileSize.textContent = this.formatFileSize(file.size);
        this.fileInfo.classList.remove('hidden');
        this.fileConfirmBtn.disabled = false;

        this.addLogMessage(`已选择固件文件: ${file.name} (${this.formatFileSize(file.size)})`);
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    async confirmFileSelection() {
        if (!this.firmwareFile) {
            this.addLogMessage('请先选择固件文件', 'warning');
            return;
        }

        this.hideFileDialog();
        await this.startFirmwareDownload();
    }

    // 固件下载核心逻辑
    async startFirmwareDownload() {
        this.isDownloading = true;
        this.updateButtonStates();
        this.updateProgress(0);

        this.addLogMessage('开始固件下载...');

        try {
            // 1. 写标志位
            this.addLogMessage('步骤1: 写入标志位');
            const flagResult = await this.writeFlag();
            if (!flagResult.success) {
                throw new Error('写入标志位失败: ' + flagResult.message);
            }

            // 2. 上传固件文件
            this.addLogMessage('步骤2: 上传固件数据');
            const uploadResult = await this.uploadFirmware();
            if (!uploadResult.success) {
                throw new Error('上传固件失败: ' + uploadResult.message);
            }

            // 3. 写版本信息
            this.addLogMessage('步骤3: 写入版本信息');
            const versionResult = await this.writeVersion();
            if (!versionResult.success) {
                throw new Error('写入版本信息失败: ' + versionResult.message);
            }

            // 4. 测试运行
            this.addLogMessage('步骤4: 测试运行');
            const testResult = await this.testRun();
            if (!testResult.success) {
                throw new Error('测试运行失败: ' + testResult.message);
            }

            this.updateProgress(100);
            this.addLogMessage('固件下载完成！', 'success');

        } catch (error) {
            this.addLogMessage('固件下载失败: ' + error.message, 'error');
        } finally {
            this.isDownloading = false;
            this.updateButtonStates();
        }
    }

    async writeFlag() {
        const response = await fetch('/api/bootload/write-flag', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                mode: this.currentMode,
                codeArea: this.config[this.currentMode].codeArea
            })
        });

        return await response.json();
    }

    async uploadFirmware() {
        const formData = new FormData();
        formData.append('firmware', this.firmwareFile);
        formData.append('mode', this.currentMode);
        formData.append('config', JSON.stringify(this.config[this.currentMode]));

        const response = await fetch('/api/bootload/upload-firmware', {
            method: 'POST',
            body: formData
        });

        // 监听上传进度
        if (response.body) {
            const reader = response.body.getReader();
            const decoder = new TextDecoder();

            while (true) {
                const { done, value } = await reader.read();
                if (done) break;

                const chunk = decoder.decode(value);
                const lines = chunk.split('\n');

                for (const line of lines) {
                    if (line.trim()) {
                        try {
                            const data = JSON.parse(line);
                            if (data.progress !== undefined) {
                                this.updateProgress(data.progress);
                                this.addLogMessage(`下载进度: ${data.progress.toFixed(1)}%`);
                            }
                        } catch (e) {
                            // 忽略解析错误
                        }
                    }
                }
            }
        }

        return await response.json();
    }

    async writeVersion() {
        const response = await fetch('/api/bootload/write-version', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                mode: this.currentMode,
                codeArea: this.config[this.currentMode].codeArea
            })
        });

        return await response.json();
    }

    async testRun() {
        const response = await fetch('/api/bootload/test-run', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                mode: this.currentMode
            })
        });

        return await response.json();
    }

    updateProgress(percentage) {
        this.downloadProgress = percentage;
        this.progressFill.style.width = `${percentage}%`;
        this.progressText.textContent = `${percentage.toFixed(1)}%`;
    }
}

// 初始化
document.addEventListener('DOMContentLoaded', function () {
    window.bootloadManager = new BootloadManager();
});
