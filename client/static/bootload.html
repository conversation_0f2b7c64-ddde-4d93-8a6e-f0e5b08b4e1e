<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>固件下载工具 V2.10</title>
    <link rel="stylesheet" href="/static/style.css?v=20250730">
    <link rel="stylesheet" href="/static/bootload.css?v=20250730">
</head>
<body>
    <div class="container">
        <!-- 顶部导航 -->
        <div class="toolbar">
            <div class="main-toolbar">
                <div class="status-section">
                    <span id="connection-status" class="status-indicator offline">🔴</span>
                    <span id="device-id">固件下载工具</span>
                    <span id="version-info">版本: V2.10</span>
                </div>
                <button id="back-btn" class="btn btn-secondary">返回参数设置</button>
            </div>
        </div>

        <!-- 模式切换 -->
        <div class="mode-switch-container">
            <div class="mode-switch">
                <button id="can-mode-btn" class="mode-btn active">CAN下载</button>
                <button id="ethernet-mode-btn" class="mode-btn">以太网下载</button>
            </div>
        </div>

        <!-- CAN下载界面 -->
        <div id="can-download-panel" class="download-panel active">
            <div class="panel-header">
                <h2>CAN总线固件下载</h2>
            </div>
            
            <div class="config-section">
                <div class="config-row">
                    <label>波特率:</label>
                    <select id="can-baudrate">
                        <option value="1000K">1000K</option>
                        <option value="800K">800K</option>
                        <option value="500K" selected>500K</option>
                        <option value="250K">250K</option>
                        <option value="125K">125K</option>
                        <option value="100K">100K</option>
                        <option value="50K">50K</option>
                        <option value="20K">20K</option>
                        <option value="10K">10K</option>
                        <option value="5K">5K</option>
                    </select>
                </div>
                
                <div class="config-row">
                    <label>代码区域:</label>
                    <select id="can-code-area">
                        <option value="1" selected>区域1</option>
                        <option value="2">区域2</option>
                        <option value="3">区域3</option>
                    </select>
                </div>
                
                <div class="config-row">
                    <label>通信ID:</label>
                    <input type="text" id="can-com-id" value="0x123" placeholder="0x123">
                </div>
            </div>

            <div class="action-section">
                <button id="can-connect-btn" class="btn btn-primary">连接</button>
                <button id="can-download-btn" class="btn btn-success" disabled>下载</button>
                <button id="can-clear-flag-btn" class="btn btn-warning" disabled>清标志位</button>
            </div>
        </div>

        <!-- 以太网下载界面 -->
        <div id="ethernet-download-panel" class="download-panel">
            <div class="panel-header">
                <h2>以太网固件下载</h2>
            </div>
            
            <div class="config-section">
                <div class="config-row">
                    <label>本地IP:</label>
                    <select id="ethernet-local-ip">
                        <option value="auto">自动检测</option>
                    </select>
                </div>
                
                <div class="config-row">
                    <label>本地端口:</label>
                    <input type="number" id="ethernet-local-port" value="7811" min="1" max="65535">
                </div>
                
                <div class="config-row">
                    <label>目标IP:</label>
                    <input type="text" id="ethernet-target-ip" value="*************" placeholder="*************">
                </div>
                
                <div class="config-row">
                    <label>目标端口:</label>
                    <input type="number" id="ethernet-target-port" value="7812" min="1" max="65535">
                </div>
                
                <div class="config-row">
                    <label>代码区域:</label>
                    <select id="ethernet-code-area">
                        <option value="1" selected>区域1</option>
                        <option value="2">区域2</option>
                        <option value="3">区域3</option>
                    </select>
                </div>
                
                <div class="config-row">
                    <label>参数目标IP:</label>
                    <input type="text" id="ethernet-para-target-ip" value="*************" placeholder="*************">
                    <button id="ethernet-modify-para-ip-btn" class="btn btn-small">修改</button>
                </div>
            </div>

            <div class="action-section">
                <button id="ethernet-connect-btn" class="btn btn-primary">连接</button>
                <button id="ethernet-download-btn" class="btn btn-success" disabled>下载</button>
                <button id="ethernet-clear-flag-btn" class="btn btn-warning" disabled>清标志位</button>
            </div>
        </div>

        <!-- 进度显示 -->
        <div class="progress-section">
            <div class="progress-container">
                <div class="progress-bar">
                    <div id="progress-fill" class="progress-fill"></div>
                </div>
                <span id="progress-text">0%</span>
            </div>
        </div>

        <!-- 日志显示区域 -->
        <div class="log-section">
            <div class="log-header">
                <h3>操作日志</h3>
                <button id="clear-log-btn" class="btn btn-small">清空日志</button>
            </div>
            <div id="log-container" class="log-container">
                <div class="log-item">固件下载工具已启动</div>
            </div>
        </div>

        <!-- 文件选择对话框 -->
        <div id="file-dialog" class="modal hidden">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>选择固件文件</h3>
                    <button id="file-dialog-close" class="modal-close">×</button>
                </div>
                <div class="modal-body">
                    <div class="file-drop-zone" id="file-drop-zone">
                        <div class="drop-zone-content">
                            <div class="drop-zone-icon">📁</div>
                            <p>拖拽固件文件到此处，或点击选择文件</p>
                            <p class="file-hint">支持 .bin, .hex 格式</p>
                        </div>
                        <input type="file" id="firmware-file-input" accept=".bin,.hex" style="display: none;">
                    </div>
                    <div id="file-info" class="file-info hidden">
                        <div class="file-details">
                            <span id="file-name"></span>
                            <span id="file-size"></span>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button id="file-confirm-btn" class="btn btn-primary" disabled>确认下载</button>
                    <button id="file-cancel-btn" class="btn btn-secondary">取消</button>
                </div>
            </div>
        </div>
    </div>

    <script src="/static/bootload.js"></script>
</body>
</html>
