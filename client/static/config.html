<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>配置管理 - 参数设置系统</title>
    <link rel="stylesheet" href="/static/style.css">
    <style>
        .config-container {
            max-width: 800px;
            margin: 20px auto;
            padding: 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .config-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            padding-bottom: 15px;
            border-bottom: 2px solid #eee;
        }

        .config-form {
            display: grid;
            gap: 20px;
        }

        .config-group {
            border: 1px solid #ddd;
            border-radius: 6px;
            padding: 20px;
            background: #fafafa;
        }

        .config-group h3 {
            margin: 0 0 15px 0;
            color: #333;
            font-size: 16px;
            border-bottom: 1px solid #ddd;
            padding-bottom: 8px;
        }

        .config-item {
            display: grid;
            grid-template-columns: 200px 1fr;
            gap: 15px;
            align-items: center;
            margin-bottom: 15px;
        }

        .config-item:last-child {
            margin-bottom: 0;
        }

        .config-label {
            font-weight: 500;
            color: #555;
        }

        .config-input {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }

        .config-input:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
        }

        .config-actions {
            display: flex;
            gap: 10px;
            justify-content: flex-end;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #eee;
        }

        .server-list {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin-top: 10px;
        }

        .server-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 5px 0;
            border-bottom: 1px solid #eee;
        }

        .server-item:last-child {
            border-bottom: none;
        }

        .server-status {
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        .server-status.active {
            background: #d4edda;
            color: #155724;
        }

        .server-status.inactive {
            background: #f8d7da;
            color: #721c24;
        }
    </style>
</head>

<body>
    <div class="config-container">
        <div class="config-header">
            <h1>配置管理</h1>
            <button id="back-btn" class="btn btn-secondary">返回主页</button>
        </div>

        <form id="config-form" class="config-form">
            <div class="config-group">
                <h3>Web服务配置</h3>
                <div class="config-item">
                    <label class="config-label">Web端口:</label>
                    <input type="number" id="WEB_PORT" class="config-input" min="1000" max="65535">
                </div>
            </div>

            <div class="config-group">
                <h3>服务器配置</h3>
                <div class="config-item">
                    <label class="config-label">主服务器地址:</label>
                    <input type="text" id="SERVER_HOST" class="config-input" placeholder="*************">
                </div>
                <div class="config-item">
                    <label class="config-label">主服务器端口:</label>
                    <input type="number" id="SERVER_PORT" class="config-input" min="1000" max="65535"
                        placeholder="8888">
                </div>
                <div class="server-list">
                    <div class="server-item">
                        <span>默认服务器: **************:7777</span>
                        <span id="default-server-status" class="server-status active">启用</span>
                    </div>
                    <div class="server-item">
                        <span id="custom-server-display">自定义服务器: --</span>
                        <span id="custom-server-status" class="server-status inactive">未配置</span>
                    </div>
                    <div class="server-item" style="border-top: 1px solid #ccc; margin-top: 10px; padding-top: 10px;">
                        <span><strong>连接状态:</strong></span>
                        <span id="connection-summary" class="server-status inactive">检查中...</span>
                    </div>
                </div>
            </div>

            <div class="config-group">
                <h3>设备配置</h3>
                <div class="config-item">
                    <label class="config-label">设备ID:</label>
                    <input type="text" id="DEVICE_ID" class="config-input" placeholder="太阳能通信车001">
                </div>
            </div>

            <div class="config-group">
                <h3>下位机配置</h3>
                <div class="config-item">
                    <label class="config-label">下位机IP:</label>
                    <input type="text" id="UDP_TARGET_IP" class="config-input" placeholder="*************">
                </div>
                <div class="config-item">
                    <label class="config-label">下位机端口:</label>
                    <input type="number" id="UDP_TARGET_PORT" class="config-input" min="1000" max="65535"
                        placeholder="8080">
                </div>
                <div class="config-item">
                    <label class="config-label">UDP监听端口:</label>
                    <input type="number" id="UDP_PORT" class="config-input" min="1000" max="65535">
                </div>
            </div>



            <div class="config-group">
                <h3>系统控制</h3>
                <div class="config-item">
                    <label class="config-label">管理员密码:</label>
                    <input type="password" id="admin-password" class="config-input" placeholder="输入sudo密码（默认：orangepi）"
                        value="orangepi" style="font-family: monospace;">
                </div>
                <div class="config-item">
                    <label class="config-label">服务控制:</label>
                    <div style="display: flex; gap: 10px;">
                        <button type="button" id="restart-service-btn" class="btn btn-warning">重启服务</button>
                        <button type="button" id="restart-system-btn" class="btn btn-danger">重启计算机</button>
                    </div>
                </div>
                <div class="config-item">
                    <label class="config-label">说明:</label>
                    <div style="font-size: 12px; color: #666; line-height: 1.4;">
                        • 重启服务：重启参数设置客户端服务<br>
                        • 重启计算机：重启整个系统（需要管理员权限）<br>
                        • 默认密码为 orangepi，如已修改请输入正确密码
                    </div>
                </div>
            </div>

            <div class="config-actions">
                <button type="button" id="reset-btn" class="btn btn-secondary">重置默认</button>
                <button type="button" id="save-btn" class="btn btn-primary">保存配置</button>
            </div>
        </form>
    </div>

    <!-- 成功/错误提示 -->
    <div id="message-container"></div>

    <script src="/static/config.js"></script>
</body>

</html>