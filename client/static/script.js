// 全局变量
let parameters = [];
let filteredParameters = [];
let isLoading = false;
let parameterMappings = {}; // 存储参数映射

// DOM元素
const elements = {
    readAllBtn: document.getElementById('read-all-btn'),
    bootloadBtn: document.getElementById('bootload-btn'),
    searchInput: document.getElementById('search-input'),
    uploadBtn: document.getElementById('upload-btn'),
    loading: document.getElementById('loading'),
    errorMessage: document.getElementById('error-message'),
    errorText: document.getElementById('error-text'),
    errorClose: document.getElementById('error-close'),
    noParameters: document.getElementById('no-parameters'),
    parametersGrid: document.getElementById('parameters-grid'),
    connectionStatus: document.getElementById('connection-status'),
    paramCount: document.getElementById('param-count'),
    lastUpdate: document.getElementById('last-update'),
    deviceId: document.getElementById('device-id'),
    confirmDialog: document.getElementById('confirm-dialog'),
    confirmParamName: document.getElementById('confirm-param-name'),
    confirmOldValue: document.getElementById('confirm-old-value'),
    confirmNewValue: document.getElementById('confirm-new-value'),
    confirmYes: document.getElementById('confirm-yes'),
    confirmNo: document.getElementById('confirm-no'),
    uploadDialog: document.getElementById('upload-dialog'),
    fileInput: document.getElementById('file-input'),
    dropZone: document.getElementById('drop-zone'),
    fileInfo: document.getElementById('file-info'),
    fileName: document.getElementById('file-name'),
    fileSize: document.getElementById('file-size'),
    uploadConfirm: document.getElementById('upload-confirm'),
    uploadCancel: document.getElementById('upload-cancel'),
    configBtn: document.getElementById('config-btn')
};

// 初始化
document.addEventListener('DOMContentLoaded', function () {
    initializeEventListeners();
    checkServerStatus();
    loadDeviceInfo();
    checkDeviceConnection();
    loadDefaultMappings(); // 加载默认映射

    // 定期检测设备连接状态（每10秒）
    setInterval(checkDeviceConnection, 10000);
});

// 事件监听器
function initializeEventListeners() {
    elements.readAllBtn.addEventListener('click', readAllParameters);
    elements.bootloadBtn.addEventListener('click', openBootloadPage);
    elements.searchInput.addEventListener('input', filterParameters);
    elements.uploadBtn.addEventListener('click', showUploadDialog);
    elements.errorClose.addEventListener('click', hideError);
    elements.confirmYes.addEventListener('click', confirmParameterUpdate);
    elements.confirmNo.addEventListener('click', hideConfirmDialog);
    elements.configBtn.addEventListener('click', openConfigPage);

    // 文件上传相关事件
    elements.uploadCancel.addEventListener('click', hideUploadDialog);
    elements.uploadConfirm.addEventListener('click', handleFileUpload);
    elements.dropZone.addEventListener('click', () => elements.fileInput.click());
    elements.fileInput.addEventListener('change', handleFileSelect);

    // 拖拽上传
    elements.dropZone.addEventListener('dragenter', handleDragEnter);
    elements.dropZone.addEventListener('dragover', handleDragOver);
    elements.dropZone.addEventListener('dragleave', handleDragLeave);
    elements.dropZone.addEventListener('drop', handleFileDrop);

    // 回车键搜索
    elements.searchInput.addEventListener('keypress', function (e) {
        if (e.key === 'Enter') {
            filterParameters();
        }
    });

    // 日志管理按钮
    const logManagerBtn = document.getElementById('log-manager-btn');
    if (logManagerBtn) {
        logManagerBtn.addEventListener('click', function () {
            window.open('/log-manager.html', '_blank');
        });
    }
}

// 检查服务器状态
async function checkServerStatus() {
    try {
        const response = await fetch('/api/status');
        const data = await response.json();

        if (data.success) {
            console.log('Web服务器状态正常');
            // 不要在这里更新连接状态，因为这只是Web服务器状态
            // 下位机连接状态由checkDeviceConnection()单独处理
        } else {
            console.log('Web服务器状态异常');
        }
    } catch (error) {
        console.error('检查服务器状态失败:', error);
        // 只有在Web服务器完全无法访问时才显示离线
        // 但这种情况下页面本身也无法加载，所以这里不需要处理
    }
}

// 更新连接状态
function updateConnectionStatus(isOnline) {
    if (isOnline) {
        elements.connectionStatus.textContent = '🟢';
        elements.connectionStatus.className = 'status-indicator online';
        elements.connectionStatus.title = '下位机在线';
    } else {
        elements.connectionStatus.textContent = '🔴';
        elements.connectionStatus.className = 'status-indicator offline';
        elements.connectionStatus.title = '下位机离线';
    }
}

// 加载设备信息
async function loadDeviceInfo() {
    try {
        const response = await fetch('/api/device-info');
        const data = await response.json();

        if (data.success) {
            // 更新设备ID显示（只显示内容，不显示"设备ID:"前缀）
            elements.deviceId.textContent = data.data.device_id;
            console.log('设备信息:', data.data);
        }
    } catch (error) {
        console.error('加载设备信息失败:', error);
    }
}

// 刷新设备信息（用于配置更新后）
async function refreshDeviceInfo() {
    await loadDeviceInfo();
    console.log('设备信息已刷新');
}

// 检测下位机连接状态（仅检测连接，不刷新参数）
async function checkDeviceConnection() {
    try {
        const response = await fetch('/api/ping-device');
        const data = await response.json();

        // 根据实际检测结果更新连接状态
        updateConnectionStatus(data.success && data.data.online);

        if (data.success) {
            console.log('设备连接检测结果: 在线状态 =', data.data.online);
        }
    } catch (error) {
        console.error('检测设备连接失败:', error);
        updateConnectionStatus(false);
    }
}

// 读取所有参数
async function readAllParameters() {
    if (isLoading) return;

    setLoading(true);
    hideError();

    try {
        console.log('开始强制读取参数...');
        const response = await fetch('/api/parameters?force_refresh=true');
        console.log('API响应状态:', response.status);

        const data = await response.json();
        console.log('API响应数据:', data);

        if (data.success) {
            parameters = data.data || [];
            filteredParameters = [...parameters];
            console.log('解析到参数数量:', parameters.length);

            displayParameters();
            updateParameterCount();
            updateLastUpdateTime();
            showSuccess(`成功读取 ${parameters.length} 个参数`);
        } else {
            console.error('API返回失败:', data);
            showError(data.message || '读取参数失败');
        }
    } catch (error) {
        console.error('网络请求失败:', error);
        showError('网络错误，请检查连接');
    } finally {
        setLoading(false);
    }
}

// 显示参数
function displayParameters() {
    if (filteredParameters.length === 0) {
        elements.noParameters.classList.remove('hidden');
        elements.parametersGrid.classList.add('hidden');
        return;
    }

    elements.noParameters.classList.add('hidden');
    elements.parametersGrid.classList.remove('hidden');

    elements.parametersGrid.innerHTML = '';

    filteredParameters.forEach((param, index) => {
        const paramElement = createParameterElement(param, index);
        elements.parametersGrid.appendChild(paramElement);
    });
}

// 创建参数元素
function createParameterElement(param, index) {
    const div = document.createElement('div');
    div.className = 'parameter-item';

    // 获取参数的中文映射
    const mapping = parameterMappings[param.name];
    const mappingHtml = mapping ? `<span class="param-mapping">${escapeHtml(mapping)}</span>` : '';

    div.innerHTML = `
        <div class="parameter-name">${escapeHtml(param.name)}${mappingHtml}</div>
        <div class="parameter-value">
            <input type="number"
                   class="parameter-input"
                   value="${param.value}"
                   step="any"
                   data-original="${param.value}"
                   data-name="${escapeHtml(param.name)}"
                   data-index="${index}">
            <div class="parameter-buttons">
                <button class="read-btn" onclick="readParameter(${index})" title="从下位机读取最新值">读取</button>
                <button class="update-btn" onclick="updateParameter(${index})" title="将参数值写入下位机">写入</button>
            </div>
        </div>
    `;

    // 添加输入变化监听
    const input = div.querySelector('.parameter-input');
    input.addEventListener('input', function () {
        const original = parseFloat(this.dataset.original);
        const current = parseFloat(this.value);

        if (Math.abs(current - original) > 0.0001) {
            this.classList.add('modified');
        } else {
            this.classList.remove('modified');
        }
    });

    return div;
}

// 读取单个参数
async function readParameter(index) {
    const param = filteredParameters[index];
    const input = document.querySelector(`input[data-index="${index}"]`);
    const readBtn = document.querySelector(`input[data-index="${index}"]`).parentElement.querySelector('.read-btn');

    try {
        // 显示加载状态
        readBtn.disabled = true;
        readBtn.innerHTML = '读取中...';

        const response = await fetch(`/api/parameters/${encodeURIComponent(param.name)}`, {
            method: 'GET'
        });

        const data = await response.json();

        if (data.success) {
            // 更新输入框的值
            const newValue = data.data.value;
            input.value = newValue;
            input.dataset.original = newValue;
            input.classList.remove('modified');

            // 更新本地数据
            param.value = newValue;

            showSuccess(`参数 ${param.name} 读取成功: ${newValue}`);
        } else {
            showError(data.message || `读取参数 ${param.name} 失败`);
        }
    } catch (error) {
        console.error('读取参数失败:', error);
        showError(`读取参数 ${param.name} 失败: ${error.message}`);
    } finally {
        // 恢复按钮状态
        readBtn.disabled = false;
        readBtn.innerHTML = '读取';
    }
}

// 写入参数
async function updateParameter(index) {
    const param = filteredParameters[index];
    const input = document.querySelector(`input[data-index="${index}"]`);
    const updateBtn = input.parentElement.querySelector('.update-btn');
    const newValue = parseFloat(input.value);
    const oldValue = parseFloat(input.dataset.original);

    if (Math.abs(newValue - oldValue) < 0.0001) {
        showError('参数值未改变');
        return;
    }

    try {
        // 显示写入状态
        updateBtn.disabled = true;
        updateBtn.innerHTML = '写入中...';

        const response = await fetch(`/api/parameters/${encodeURIComponent(param.name)}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ value: newValue })
        });

        const data = await response.json();

        if (data.success) {
            // 更新本地数据
            param.value = newValue;
            input.dataset.original = newValue;
            input.classList.remove('modified');

            // 显示写入成功消息
            showSuccess(`参数 ${param.name} 写入成功: ${newValue}`);
        } else {
            showError(data.message || '写入参数失败');
            // 恢复原值
            input.value = oldValue;
        }
    } catch (error) {
        showError('网络错误，请检查连接');
        console.error('写入参数失败:', error);
        // 恢复原值
        input.value = oldValue;
    } finally {
        // 恢复按钮状态
        updateBtn.disabled = false;
        updateBtn.innerHTML = '写入';
    }
}

// 显示确认对话框
function showConfirmDialog(paramName, oldValue, newValue, onConfirm) {
    elements.confirmParamName.textContent = paramName;
    elements.confirmOldValue.textContent = oldValue.toFixed(6);
    elements.confirmNewValue.textContent = newValue.toFixed(6);
    elements.confirmDialog.classList.remove('hidden');

    // 保存确认回调
    elements.confirmDialog.onConfirm = onConfirm;
}

// 隐藏确认对话框
function hideConfirmDialog() {
    elements.confirmDialog.classList.add('hidden');
    elements.confirmDialog.onConfirm = null;
}

// 确认参数更新
function confirmParameterUpdate() {
    if (elements.confirmDialog.onConfirm) {
        elements.confirmDialog.onConfirm();
    }
    hideConfirmDialog();
}

// 过滤参数
function filterParameters() {
    const searchTerm = elements.searchInput.value.toLowerCase().trim();

    if (searchTerm === '') {
        filteredParameters = [...parameters];
    } else {
        filteredParameters = parameters.filter(param => {
            // 搜索参数名
            const nameMatch = param.name.toLowerCase().includes(searchTerm);

            // 搜索中文映射
            const mapping = parameterMappings[param.name];
            const mappingMatch = mapping ? mapping.toLowerCase().includes(searchTerm) : false;

            // 只要参数名或中文映射匹配就显示
            return nameMatch || mappingMatch;
        });
    }

    displayParameters();
    updateParameterCount();
}

// 设置加载状态
function setLoading(loading) {
    isLoading = loading;

    if (loading) {
        elements.loading.classList.remove('hidden');
        elements.readAllBtn.disabled = true;
        elements.readAllBtn.innerHTML = '<span class="btn-icon">⏳</span>读取中...';
    } else {
        elements.loading.classList.add('hidden');
        elements.readAllBtn.disabled = false;
        elements.readAllBtn.innerHTML = '<span class="btn-icon">📖</span>读取所有参数';
    }
}

// 显示错误
function showError(message) {
    elements.errorText.textContent = message;
    elements.errorMessage.classList.remove('hidden');

    // 3秒后自动隐藏
    setTimeout(hideError, 3000);
}

// 隐藏错误
function hideError() {
    elements.errorMessage.classList.add('hidden');
}

// 显示成功消息
function showSuccess(message) {
    // 简单的成功提示，可以扩展为更好的UI
    console.log('成功:', message);
}

// 更新参数数量
function updateParameterCount() {
    const total = parameters.length;
    const filtered = filteredParameters.length;

    if (total === filtered) {
        elements.paramCount.textContent = `参数数量: ${total}`;
    } else {
        elements.paramCount.textContent = `参数数量: ${filtered}/${total}`;
    }
}

// 更新最后更新时间
function updateLastUpdateTime() {
    const now = new Date();
    const timeString = now.toLocaleTimeString('zh-CN');
    elements.lastUpdate.textContent = `最后更新: ${timeString}`;
}

// HTML转义
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

// 定期检查连接状态
setInterval(checkServerStatus, 30000); // 每30秒检查一次

// 加载默认参数映射
async function loadDefaultMappings() {
    try {
        const response = await fetch('/api/parameter-mappings');
        if (response.ok) {
            const data = await response.json();
            if (data.success && data.data.mappings) {
                parameterMappings = data.data.mappings;
                console.log('已加载默认参数映射:', Object.keys(parameterMappings).length, '个');
                // 如果已有参数显示，重新渲染以应用映射
                if (parameters.length > 0) {
                    displayParameters();
                }
            }
        }
    } catch (error) {
        console.log('加载默认映射失败，使用空映射:', error.message);
        parameterMappings = {};
    }
}

// 文件上传相关函数
function showUploadDialog() {
    elements.uploadDialog.classList.remove('hidden');
    resetUploadDialog();
}

function hideUploadDialog() {
    elements.uploadDialog.classList.add('hidden');
    resetUploadDialog();
}

function resetUploadDialog() {
    elements.fileInput.value = '';
    elements.fileInfo.classList.add('hidden');
    elements.uploadConfirm.disabled = true;
    elements.dropZone.classList.remove('dragover');
}

function handleFileSelect(event) {
    const file = event.target.files[0];
    if (file) {
        validateAndShowFile(file);
    }
}

function handleDragEnter(event) {
    event.preventDefault();
    elements.dropZone.classList.add('dragover');
}

function handleDragOver(event) {
    event.preventDefault();
    // 保持dragover样式
}

function handleDragLeave(event) {
    event.preventDefault();
    // 只有当离开dropZone本身时才移除样式，避免子元素触发
    if (!elements.dropZone.contains(event.relatedTarget)) {
        elements.dropZone.classList.remove('dragover');
    }
}

function handleFileDrop(event) {
    console.log('文件拖拽放下');
    event.preventDefault();
    elements.dropZone.classList.remove('dragover');

    const files = event.dataTransfer.files;
    console.log('拖拽的文件数量:', files.length);

    if (files.length > 0) {
        console.log('拖拽的文件:', files[0]);

        // 手动设置文件输入框的文件
        try {
            const dt = new DataTransfer();
            dt.items.add(files[0]);
            elements.fileInput.files = dt.files;
            console.log('文件已设置到input元素');
        } catch (error) {
            console.error('设置文件到input失败:', error);
        }

        validateAndShowFile(files[0]);
    }
}

function validateAndShowFile(file) {
    // 检查文件类型
    if (!file.name.toLowerCase().endsWith('.json')) {
        showError('请选择JSON格式的文件');
        return;
    }

    // 检查文件大小（限制为1MB）
    if (file.size > 1024 * 1024) {
        showError('文件大小不能超过1MB');
        return;
    }

    // 显示文件信息
    elements.fileName.textContent = file.name;
    elements.fileSize.textContent = formatFileSize(file.size);
    elements.fileInfo.classList.remove('hidden');
    elements.uploadConfirm.disabled = false;
}

function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function handleFileUpload() {
    console.log('handleFileUpload 被调用');
    const file = elements.fileInput.files[0];
    console.log('选择的文件:', file);

    if (!file) {
        console.log('没有选择文件');
        showError('请先选择文件');
        return;
    }

    const reader = new FileReader();
    reader.onload = async function (e) {
        try {
            console.log('文件读取完成');
            const content = e.target.result;
            console.log('文件内容:', content);
            parseParameterMappings(content);

            // 保存映射到服务器
            await saveMappingsToServer();

            hideUploadDialog();
            showSuccess('参数映射文件上传成功');
            // 重新显示参数以应用映射
            displayParameters();
        } catch (error) {
            console.error('文件解析错误:', error);
            showError('文件解析失败: ' + error.message);
        }
    };

    reader.onerror = function (e) {
        console.error('文件读取错误:', e);
        showError('文件读取失败');
    };

    console.log('开始读取文件');
    reader.readAsText(file);
}

function parseParameterMappings(content) {
    // 清空现有映射
    parameterMappings = {};

    // 按行分割内容
    const lines = content.split('\n');

    for (let line of lines) {
        line = line.trim();

        // 跳过空行
        if (!line) continue;

        // 检查是否以双斜杠开头
        if (line.startsWith('//')) {
            // 移除开头的双斜杠和空格
            line = line.substring(2).trim();

            // 查找分隔符（空格或逗号）
            let separatorIndex = -1;
            for (let i = 0; i < line.length; i++) {
                if (line[i] === ' ' || line[i] === ',') {
                    separatorIndex = i;
                    break;
                }
            }

            if (separatorIndex > 0) {
                const paramName = line.substring(0, separatorIndex).trim();
                const description = line.substring(separatorIndex + 1).trim();

                if (paramName && description) {
                    parameterMappings[paramName] = description;
                }
            }
        }
    }

    console.log('解析到的参数映射:', parameterMappings);
}

// 保存映射到服务器
async function saveMappingsToServer() {
    try {
        const mappingData = {
            mappings: parameterMappings,
            last_updated: new Date().toISOString(),
            version: "1.0.0"
        };

        const response = await fetch('/api/parameter-mappings', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(mappingData)
        });

        if (response.ok) {
            console.log('映射已保存到服务器');
        } else {
            console.warn('保存映射到服务器失败');
        }
    } catch (error) {
        console.error('保存映射失败:', error);
    }
}

function showSuccess(message) {
    // 临时显示成功消息
    const successDiv = document.createElement('div');
    successDiv.className = 'success-message';
    successDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background-color: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
        border-radius: 4px;
        padding: 12px 16px;
        z-index: 1000;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    `;
    successDiv.textContent = message;

    document.body.appendChild(successDiv);

    setTimeout(() => {
        document.body.removeChild(successDiv);
    }, 3000);
}

// 打开配置页面
function openConfigPage() {
    window.location.href = '/config.html';
}

// 打开固件下载页面
function openBootloadPage() {
    window.location.href = '/bootload.html';
}
