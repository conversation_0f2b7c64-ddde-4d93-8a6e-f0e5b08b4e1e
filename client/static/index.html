<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>参数设置 v1.0</title>
    <link rel="stylesheet" href="/static/style.css?v=20250730">
</head>

<body>
    <div class="container">
        <div class="toolbar">
            <div class="main-toolbar">
                <div class="status-section">
                    <span id="connection-status" class="status-indicator offline">🔴</span>
                    <span id="device-id">太阳能通信车</span>
                    <span id="param-count">参数数量: 0</span>
                    <span id="last-update">最后更新: --</span>
                </div>
                <button id="read-all-btn" class="btn btn-primary">
                    读取所有参数
                </button>
                <button id="bootload-btn" class="btn btn-primary" title="固件下载">
                    固件下载
                </button>
                <div class="search-box">
                    <input type="text" id="search-input" placeholder="搜索参数名或中文描述..." />
                    <button id="upload-btn" class="btn btn-primary" title="上传参数映射文件">上传</button>
                </div>
                <button id="log-manager-btn" class="btn btn-primary" title="日志管理">日志</button>
                <button id="config-btn" class="btn btn-primary" title="配置管理">配置</button>
            </div>
        </div>

        <div class="content">
            <div id="loading" class="loading hidden">
                <div class="spinner"></div>
                <p>正在读取参数，请稍候...</p>
            </div>

            <div id="error-message" class="error-message hidden">
                <span class="error-icon">⚠️</span>
                <span id="error-text">发生错误</span>
                <button id="error-close" class="error-close">×</button>
            </div>

            <div id="parameters-container" class="parameters-container">
                <div id="no-parameters" class="no-data">
                    <p>暂无参数数据</p>
                    <p>点击"读取所有参数"按钮开始</p>
                </div>

                <div id="parameters-grid" class="parameters-grid hidden">
                    <!-- 参数项将通过JavaScript动态生成 -->
                </div>
            </div>
        </div>

        <footer class="footer">
            <div class="footer-info">
                <span>© 2025 参数设置系统</span>
                <span id="version-info">v1.0.3</span>
            </div>
        </footer>
    </div>

    <!-- 确认对话框 -->
    <div id="confirm-dialog" class="modal hidden">
        <div class="modal-content">
            <h3>确认修改参数</h3>
            <p>
                参数名: <strong id="confirm-param-name"></strong><br>
                原值: <strong id="confirm-old-value"></strong><br>
                新值: <strong id="confirm-new-value"></strong>
            </p>
            <div class="modal-buttons">
                <button id="confirm-yes" class="btn btn-primary">确认</button>
                <button id="confirm-no" class="btn btn-secondary">取消</button>
            </div>
        </div>
    </div>

    <!-- 文件上传对话框 -->
    <div id="upload-dialog" class="modal hidden">
        <div class="modal-content">
            <h3>上传参数映射文件</h3>
            <p>请选择JSON格式的参数映射文件</p>
            <div class="upload-area">
                <input type="file" id="file-input" accept=".json" style="display: none;">
                <div id="drop-zone" class="drop-zone">
                    <div class="drop-zone-content">
                        <span class="upload-icon">📁</span>
                        <p>点击选择文件或拖拽文件到此处</p>
                        <p class="file-hint">仅支持 .json 格式文件</p>
                    </div>
                </div>
                <div id="file-info" class="file-info hidden">
                    <span id="file-name"></span>
                    <span id="file-size"></span>
                </div>
            </div>
            <div class="modal-buttons">
                <button id="upload-confirm" class="btn btn-primary" disabled>确认上传</button>
                <button id="upload-cancel" class="btn btn-secondary">取消</button>
            </div>
        </div>
    </div>

    <script src="/static/script.js?v=20250730"></script>
</body>

</html>